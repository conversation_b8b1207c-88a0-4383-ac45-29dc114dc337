<template>
  <view class="index-v">
    <!-- 固定搜索框 -->
    <view class="search-container">
      <BitSearch @search="handleSearch" />
    </view>
    
    <mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption"
      :fixed="false">
      <view class="content-container">
        <!-- 刷新动画 -->
        <view v-if="isRefreshing" class="refresh-animation">
          <view class="refresh-spinner"></view>
          <text class="refresh-text">正在刷新...</text>
        </view>
        
        <!-- 资讯卡片 -->
        <view class="consult-card" v-for="(item, index) in consultList"
          :key="index" @click="toDetail(item)">
          <!-- 新闻卡片 -->
          <view v-if="item.category === 'news'" class="news-card-content" :key="'news-' + index">
            <view class="news-image-wrapper">
              <image class="news-image" :src="this.define.baseURL + item.coverImage" mode="aspectFill"></image>
              <view class="news-category-tag">资讯</view>
            </view>
            <view class="news-content">
              <view class="news-title">{{ item.title }}</view>
              <view class="news-desc">{{ item.desc }}</view>
              <view class="news-meta">
                <view class="news-time">
                  <text class="time-icon">🕒</text>
                  {{ xunda.toDate(item.publishTime) }}
                </view>
                <view class="news-views" v-if="item.viewCount">
                  <text class="view-icon">👁</text>
                  {{ item.viewCount }}
                </view>
              </view>
            </view>
          </view>

          <!-- 视频卡片 -->
          <view v-if="item.category === 'video'" class="video-card-content" :key="'video-' + index">
            <view class="video-image-wrapper">
              <image class="video-image" :src="this.define.baseURL + item.coverImage" mode="aspectFill"></image>
              <view class="video-play-overlay">
                <view class="play-button">
                  <text class="play-icon">▶</text>
                </view>
              </view>
              <view class="video-duration-tag">{{ item.duration }}</view>
              <view class="video-category-tag">视频</view>
            </view>
            <view class="video-content">
              <view class="video-title">{{ item.title }}</view>
              <view class="video-meta">
                <view class="video-time">
                  <text class="time-icon">🕒</text>
                  {{ xunda.toDate(item.publishTime) }}
                </view>
                <view class="video-views" v-if="item.viewCount">
                  <text class="view-icon">👁</text>
                  {{ item.viewCount }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </mescroll-uni>
  </view>
</template>
<script>
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import IndexMixin from "./mixin.js";
import portalItem from "@/pages/portal/components/index.vue";
import defaultPortal from "@/pages/portal/components/defaultPortal.vue";
import PasswordPopup from "./components/PasswordPopup";
import BitSearch from "@/components/gzbit/BitSearch.vue";
import { getNewsList, updateViewCount } from '@/api/flow-up/news';
import xunda from "@/utils/xunda.js";
export default {
  mixins: [MescrollMixin, IndexMixin],
  components: {
    portalItem,
    defaultPortal,
    PasswordPopup,
    BitSearch,
  },
  data() {
    return {
      searchKeyword: '', // 搜索关键词
      isRefreshing: false, // 是否正在刷新
      upOption: {
        page: {
          num: 0,
          size: 10,
          time: null,
        },
        empty: {
          use: true,
          tip: "📰 暂无资讯内容",
          btnText: "刷新试试",
          icon: "/static/images/empty-news.png"
        },
        textNoMore: "🎉 已经到底啦，没有更多内容了",
        textLoading: "📖 正在加载精彩内容...",
      },
      consultList: [],
    };
  },
  onShow() { },
  onReady() { },
  onLoad(e) { },
  computed: {},
  methods: {
    mescrollInit(mescroll) {
      this.mescroll = mescroll;
    },
    async downCallback() {
      // 下拉刷新的回调
      try {
        this.isRefreshing = true; // 显示刷新动画
        // 重置分页参数
        this.mescroll.resetUpScroll();
        // 重新加载第一页数据
        const [newsRes] = await Promise.all([
          getNewsList({ page: 1, pageSize: 10, keyword: this.searchKeyword }),
        ]);

        if (newsRes.code === 200) {
          this.consultList = newsRes.data.list || [];
        }

        this.mescroll.endSuccess();
        this.isRefreshing = false; // 隐藏刷新动画
      } catch (error) {
        console.error('下拉刷新失败:', error);
        this.mescroll.endErr();
        this.isRefreshing = false; // 隐藏刷新动画
        uni.showToast({
          title: '刷新失败，请重试',
          icon: 'none'
        });
      }
    },

    async upCallback(page) {
      // 上拉加载的回调
      try {
        // 加载资讯列表
        const newsRes = await getNewsList({
          page: page.num,
          pageSize: page.size,
          keyword: this.searchKeyword // 添加搜索关键词
        });

        if (newsRes.code === 200) {
          const { list = [], total = 0 } = newsRes.data;
          // 如果是第一页，直接赋值
          if (page.num === 1) {
            this.consultList = list;
          } else {
            // 不是第一页，追加数据
            this.consultList = this.consultList.concat(list);
          }

          // 判断是否还有下一页
          const hasNext = this.consultList.length < total;
          this.mescroll.endSuccess(list.length, hasNext);

        } else {
          this.mescroll.endErr();
          uni.showToast({
            title: newsRes.msg || '加载失败',
            icon: 'none'
          });
        }
        
        // 确保刷新动画关闭
        if (this.isRefreshing) {
          this.isRefreshing = false;
        }
      } catch (error) {
        console.error('上拉加载失败:', error);
        this.mescroll.endErr();
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
        
        // 确保刷新动画关闭
        if (this.isRefreshing) {
          this.isRefreshing = false;
        }
      }
    },

    // 搜索功能
    async searchNews() {
      try {
        this.isRefreshing = true; // 显示刷新动画
        this.mescroll.resetUpScroll(); // 重置并重新加载数据
      } catch (error) {
        console.error('搜索失败:', error);
        this.isRefreshing = false; // 隐藏刷新动画
        uni.showToast({
          title: '搜索失败，请重试',
          icon: 'none'
        });
      }
    },
    
    // 清空搜索文本
    clearSearch() {
      this.searchKeyword = '';
      this.isRefreshing = true; // 显示刷新动画
      this.mescroll.resetUpScroll(); // 重置并重新加载数据
    },
    
    // 处理搜索事件
    async handleSearch(keyword) {
      try {
        this.searchKeyword = keyword;
        this.isRefreshing = true; // 显示刷新动画
        this.mescroll.resetUpScroll(); // 重置并重新加载数据
      } catch (error) {
        console.error('搜索失败:', error);
        this.isRefreshing = false; // 隐藏刷新动画
        uni.showToast({
          title: '搜索失败，请重试',
          icon: 'none'
        });
      }
    },
    playVideo(video) {
      uni.navigateTo({
        url: `/pages/news/video?id=${video.id}`,
      });
    },
    async toDetail(record) {
      try {
        // 异步更新阅读量，但不等待结果，让用户可以立即看到内容
        updateViewCount(record.id).catch(() => {
          // 静默处理错误，不影响用户体验
        });
        if (record.category === 'news') {
          uni.navigateTo({
            url: `/pages/news/detail?id=${record.id}`,
          });
        }
        if (record.category === 'video') {
          uni.navigateTo({
            url: `/pages/news/video?id=${record.id}`,
          });
        }
      } catch (error) {
        uni.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      }
    },
  },
};
</script>

<style lang="scss">
page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

// 自定义下拉刷新样式
::v-deep .mescroll-downwarp {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-radius: 0 0 20rpx 20rpx;

  .mescroll-downwarp-content {
    font-size: 28rpx;
    font-weight: 500;
  }
}

// 自定义上拉加载样式
::v-deep .mescroll-upwarp {
  .mescroll-upwarp-tip {
    color: #7f8c8d;
    font-size: 26rpx;
  }
}

.content-container {
  padding: 24rpx 20rpx;
  margin-top: 110rpx; // 为固定搜索框留出空间
}

// 刷新动画样式
.refresh-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  
  .refresh-spinner {
    width: 40rpx;
    height: 40rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10rpx;
  }
  
  .refresh-text {
    font-size: 28rpx;
    color: #7f8c8d;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 搜索框样式
.search-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 15rpx 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.consult-card {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    transform: translateY(-4rpx);
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
  }

  &:active {
    transform: translateY(-2rpx);
    box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.1);
  }
}

// 新闻卡片样式
.news-card-content {
  .news-image-wrapper {
    position: relative;
    width: 100%;
    height: 320rpx;
    overflow: hidden;

    .news-image {
      width: 100%;
      height: 100%;
      transition: transform 0.3s ease;
    }

    .news-category-tag {
      position: absolute;
      top: 16rpx;
      left: 16rpx;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #fff;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 22rpx;
      font-weight: 500;
      box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
    }
  }

  .news-content {
    padding: 28rpx 24rpx 24rpx;

    .news-title {
      font-size: 34rpx;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 16rpx;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .news-desc {
      font-size: 28rpx;
      color: #7f8c8d;
      margin-bottom: 20rpx;
      line-height: 1.6;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .news-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 24rpx;
      color: #95a5a6;

      .news-time,
      .news-views {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .time-icon,
        .view-icon {
          font-size: 20rpx;
        }
      }
    }
  }
}

// 视频卡片样式
.video-card-content {
  .video-image-wrapper {
    position: relative;
    width: 100%;
    height: 320rpx;
    overflow: hidden;

    .video-image {
      width: 100%;
      height: 100%;
      transition: transform 0.3s ease;
    }

    .video-play-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;

      .play-button {
        width: 80rpx;
        height: 80rpx;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;

        .play-icon {
          font-size: 32rpx;
          color: #e74c3c;
          margin-left: 4rpx;
        }
      }
    }

    .video-duration-tag {
      position: absolute;
      bottom: 16rpx;
      right: 16rpx;
      background: rgba(0, 0, 0, 0.7);
      color: #fff;
      padding: 6rpx 12rpx;
      border-radius: 12rpx;
      font-size: 22rpx;
      font-weight: 500;
    }

    .video-category-tag {
      position: absolute;
      top: 16rpx;
      left: 16rpx;
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
      color: #fff;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 22rpx;
      font-weight: 500;
      box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
    }
  }

  .video-content {
    padding: 28rpx 24rpx 24rpx;

    .video-title {
      font-size: 34rpx;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 20rpx;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .video-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 24rpx;
      color: #95a5a6;

      .video-time,
      .video-views {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .time-icon,
        .view-icon {
          font-size: 20rpx;
        }
      }
    }
  }
}

// 悬停效果
.consult-card:hover {

  .news-image,
  .video-image {
    transform: scale(1.05);
  }

  .video-play-overlay .play-button {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 1);
  }
}

// 入场动画
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-60rpx);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(60rpx);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-card-0 {
  animation: slideInUp 0.6s ease-out;
}

.animate-card-1 {
  animation: slideInLeft 0.6s ease-out 0.1s both;
}

.animate-card-2 {
  animation: slideInRight 0.6s ease-out 0.2s both;
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .content-container {
    padding: 16rpx 12rpx;
  }

  .consult-card {
    margin-bottom: 24rpx;
  }

  .news-card-content,
  .video-card-content {

    .news-image-wrapper,
    .video-image-wrapper {
      height: 280rpx;
    }

    .news-content,
    .video-content {
      padding: 20rpx 16rpx;

      .news-title,
      .video-title {
        font-size: 30rpx;
      }

      .news-desc {
        font-size: 26rpx;
      }
    }
  }
}

.index-v {
  .portal-v {
    padding: 0 20rpx 20rpx 20rpx;

    .portal-box {
      width: 100%;
      height: 100%;

      .htabs {
        .u-scroll-box {
          height: 80rpx;

          .u-tab-item {
            border-right: 1px solid #e4e7ed;

            &::before {
              content: "";
            }
          }
        }
      }

      .card-v {
        &.u-card {
          margin: 0rpx !important;
          padding: 0rpx !important;
        }
      }
    }
  }

  .nav {
    z-index: 99999;

    ::v-deep .uni-navbar__content {
      z-index: 99999;
    }

    ::v-deep .uni-navbar__header-container {
      justify-content: center;
    }

    .nav-left {
      max-width: 100%;
      display: flex;
      align-items: center;

      .nav-left-text {
        font-weight: 700;
        font-size: 32rpx;
        flex: 1;
        min-width: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .portal-select {
    background-color: #fff;
    height: 80rpx;
    padding-left: 20rpx;
    line-height: 80rpx;

    .portal-select-inner {
      width: 200rpx;
      height: 100%;

      .portal-select-text {
        color: #303133;
      }
    }

    .right-icons {
      font-weight: 700;
      margin-top: 2px;
      margin-left: 4px;
      transition-duration: 0.3s;
      color: #606266 !important;
    }

    .select-right-icons {
      transform: rotate(-180deg);
    }
  }

  .select-box {
    overflow-y: scroll;

    .search-box {
      height: 112rpx;
      width: 100%;
      padding: 20rpx 20rpx;
      z-index: 10000;
      background: #fff;

      &::after {
        content: " ";
        position: absolute;
        left: 2%;
        top: 62px;
        box-sizing: border-box;
        width: 96%;
        height: 1px;
        transform: scale(1, 0.3);
        border: 0 solid #e4e7ed;
        z-index: 2;
        border-bottom-width: 1px;
      }
    }

    .currentItem {
      color: #02a7f0;
    }

    .select-item {
      height: 88rpx;
      display: flex;
      align-items: center;
      padding: 0 20rpx;
      font-size: 30rpx;
      color: #303133;
      text-align: left;
      position: relative;

      &::after {
        content: " ";
        position: absolute;
        left: 2%;
        bottom: 0;
        box-sizing: border-box;
        width: 96%;
        height: 1px;
        transform: scale(1, 0.3);
        border: 0 solid #e4e7ed;
        z-index: 2;
        border-bottom-width: 1px;
      }

      .sysName {
        flex: 1;
        overflow: auto;
        min-width: 0;
        font-size: 28rpx;
      }
    }
  }

  .popup {
    position: absolute;
    top: 244rpx;
    z-index: 99999;
    width: 70%;
    height: 200px;
    border: 1px solid #ccc;
    background-color: #fff;
    left: 283rpx;
    border-radius: 4rpx;
    transform: translate(-50%, -50%) scale(0);
    animation: popup-animation 0.4s ease-in-out forwards;
  }

  .uni-select--mask {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: 2;
  }

  @keyframes popup-animation {
    from {
      transform: translate(-50%, -50%) scale(0);
      opacity: 0;
    }

    to {
      transform: translate(-50%, -50%) scale(1);
      opacity: 1;
    }
  }

  .nav {
    z-index: 99999;

    ::v-deep .uni-navbar__content {
      z-index: 99999;
    }

    ::v-deep .uni-navbar__header-container {
      justify-content: center;
    }

    .nav-left {
      max-width: 100%;
      display: flex;
      align-items: center;

      .nav-left-text {
        font-weight: 700;
        font-size: 29rpx;
        flex: 1;
        min-width: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.portal-nodata {
  position: absolute;
  top: 450rpx;
  width: 100%;
  text-align: center;
  z-index: 100;
  background-color: #f0f2f6;
}
</style>
