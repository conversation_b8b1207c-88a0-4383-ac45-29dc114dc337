<template>
  <view class="my-container" v-if="loading">
    <!-- 用户信息卡片 -->
    <view class="user-info-card animate__animated animate__fadeInDown">
      <view class="user-header">
        <view class="avatar-container" @click="chooseAvatar">
          <u-avatar size="120" :src="avatarSrc" class="user-avatar"></u-avatar>
        </view>
        <view class="user-details" @click="personalPage('/pages/my/personalData/index')">
          <view class="user-name">{{ bindInfo.name }}</view>
          <view class="user-role" v-if="baseInfo.roleId">{{ baseInfo.roleId }}</view>
        </view>
        <view class="arrow-icon" @click="personalPage('/pages/my/personalData/index')">
          <u-icon name="arrow-right" color="#ffffff" size="28"></u-icon>
        </view>
      </view>
    </view>

    <!-- 功能菜单区域 -->
    <view class="menu-sections">
      <!-- 常用功能 -->
      <view class="menu-section animate__animated animate__fadeInUp" :style="{ animationDelay: '0.1s' }">
        <view class="section-header">
          <text class="section-title">常用功能</text>
        </view>
        <view class="section-content">
          <view class="menu-grid">
            <view class="menu-item" @click="showQrCode()" v-if="baseInfo.roleId === '医师'">
              <view class="menu-icon bg-blue">
                <text class="icon-ym icon-ym-generator-qrcode"></text>
              </view>
              <text class="menu-text">我的二维码</text>
            </view>

            <view class="menu-item" @click="scanCode()">
              <view class="menu-icon bg-green">
                <text class="icon-ym icon-ym-scanCode1"></text>
              </view>
              <text class="menu-text">扫一扫</text>
            </view>

            <view class="menu-item" @click="openPage('/pages/my/accountSecurity/index')">
              <view class="menu-icon bg-purple">
                <text class="icon-ym icon-ym-zhanghao"></text>
              </view>
              <text class="menu-text">账号安全</text>
            </view>

            <view class="menu-item" @click="openPage('/pages/my/modifyPsd/index')">
              <view class="menu-icon bg-orange">
                <text class="icon-ym icon-ym-generator-password"></text>
              </view>
              <text class="menu-text">修改密码</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 设置与帮助 -->
      <view class="menu-section animate__animated animate__fadeInUp" :style="{ animationDelay: '0.2s' }" v-if="false">
        <view class="section-header">
          <view class="section-icon">⚙️</view>
          <text class="section-title">设置与帮助</text>
        </view>
        <view class="section-content">
          <u-cell-group :border="false">
            <u-cell-item title="我的组织" @click="openPage('/pages/my/business/index', 'Organize')"
              :title-style="titleStyle" v-if="false">
              <template #icon>
                <text class="icon-ym icon-ym-zuzhi u-m-r-16 u-font-32 my-list" />
              </template>
              <template #right-icon>
                <u-icon name="arrow-right" color="#969799" size="28"></u-icon>
              </template>
            </u-cell-item>
            <u-cell-item title="我的岗位" @click="openPage('/pages/my/business/index', 'Position')"
              :title-style="titleStyle" v-if="false">
              <template #icon>
                <text class="icon-ym icon-ym-position1 u-m-r-16 u-font-32 my-list" />
              </template>
              <template #right-icon>
                <u-icon name="arrow-right" color="#969799" size="28"></u-icon>
              </template>
            </u-cell-item>
            <u-cell-item title="我的下属" @click="openPage('/pages/my/subordinate/index')" :title-style="titleStyle"
              v-if="false">
              <template #icon>
                <text class="icon-ym icon-ym-generator-section u-m-r-16 u-font-32 my-list" />
              </template>
              <template #right-icon>
                <u-icon name="arrow-right" color="#969799" size="28"></u-icon>
              </template>
            </u-cell-item>
            <u-cell-item title="切换身份" @click="selectShow = true" v-if="userInfo.standingList?.length > 1 && false"
              :title-style="titleStyle">
              <template #icon>
                <text class="icon-ym icon-ym-header-role-toggle u-m-r-16 u-font-32 my-list" />
              </template>
              <template #right-icon>
                <u-icon name="arrow-right" color="#969799" size="28"></u-icon>
              </template>
            </u-cell-item>
            <u-cell-item title="设置" @click="openPage('/pages/my/settings/index')" :title-style="titleStyle"
              :border-bottom="false" v-if="false">
              <template #icon>
                <text class="icon-ym icon-ym-shezhi u-m-r-16 u-font-32 my-list" />
              </template>
              <template #right-icon>
                <u-icon name="arrow-right" color="#969799" size="28"></u-icon>
              </template>
            </u-cell-item>
          </u-cell-group>
        </view>
      </view>
    </view>

    <!-- 退出登录按钮 -->
    <view class="logout-section animate__animated animate__fadeInUp" :style="{ animationDelay: '0.3s' }">
      <view class="logout-btn" hover-class="button-hover" @click="logout">
        <text class="logout-text">退出登录</text>
      </view>
    </view>

    <u-select v-model="selectShow" :list="selectList" mode="single-column" value-name="id" label-name="name"
      :default-value="defaultValue" @confirm="confirm"></u-select>
  </view>
</template>
<script>
import IndexMixin from "./mixin.js";
import { getCurrentUser } from "@/api/common.js";
import { UpdateAvatar, UserSettingInfo, setMajor } from "@/api/common";
import chat from "@/libs/chat.js";
import { useUserStore } from "@/store/modules/user";
import { useChatStore } from "@/store/modules/chat";
import { generateQRCodeAsync } from "@/api/flow-up/physician";
import { getCurrentUserPatient } from '@/api/flow-up/patient';
import { getCurrentUserPhysician } from '@/api/flow-up/physician';

export default {
  mixins: [IndexMixin],
  data() {
    return {
      defaultValue: [],
      selectList: [],
      selectShow: false,
      titleStyle: {
        color: "#606266",
      },
      userInfo: "",
      avatarSrc: "",
      qrCodeSrc: "", // 二维码图片路径
      baseInfo: {},
      loading: false,
      cellItemColor: ["#6071F5", "#F4A02F", "#2B7FF0", "#4CBF2A"],
      bindInfo: {}
    };
  },
  computed: {
    baseURL() {
      return this.define.comUploadUrl;
    },
    baseURL2() {
      return this.define.baseURL;
    },
    token() {
      return uni.getStorageSync("token");
    },
    report() {
      return this.define.report;
    },
  },
  onLoad() {
    const chatStore = useChatStore();
    if (!chatStore.getSocket) chat.initSocket();
  },
  onShow() {
    UserSettingInfo().then((res) => {
      this.baseInfo = res.data || {};
      // this.avatarSrc = this.baseURL2 + this.baseInfo.avatar;
      // 生成二维码图片路径，这里使用用户ID作为二维码内容示例
      this.qrCodeSrc = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${this.baseInfo.id}`;
      this.loading = true;
      this.bindInfo = {};
      if (this.baseInfo.roleId === '患者') {
        getCurrentUserPatient().then(res => {
          this.bindInfo = res.data; if (res.data) {
            this.avatarSrc = this.define.baseURL + res.data.picture;
          }
        })
      }
      if (this.baseInfo.roleId === '医师') {
        getCurrentUserPhysician().then(res => {
          this.bindInfo = res.data; if (res.data) {
            this.avatarSrc = this.define.baseURL + res.data.picture;
          }
        })
      }
    });

    this.setStanding();
  },
  methods: {
    confirm(e) {
      if (e[0].index == this.defaultValue[0]) return;
      let data = {
        majorId: e[0].value,
        majorType: "Standing",
        menuType: 1,
      };
      const userStore = useUserStore();
      setMajor(data).then((res) => {
        this.$u.toast(res.msg);
        this.getCurrentUser();
      });
    },
    setStanding() {
      this.selectShow = false;
      this.userInfo = uni.getStorageSync("userInfo") || {};
      this.selectList = [];
      this.selectList = JSON.parse(JSON.stringify(this.userInfo.standingList));
      this.selectList.forEach((o, i) => {
        o.id = Number(this.selectList[i].id);
      });
      this.defaultValue = [this.selectList.findIndex((o) => o.currentStanding)];
    },
    getCurrentUser() {
      const userStore = useUserStore();
      userStore.getCurrentUser().then(() => {
        this.setStanding();
      });
    },
    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ["original", "compressed"],
        success: (res) => {
          // #ifdef H5
          let isAccept = new RegExp("image/*").test(res.tempFiles[0].type);
          if (!isAccept) return this.$u.toast(`请上传图片`);
          // #endif
          let tempFilePaths = res.tempFilePaths[0];
          uni.uploadFile({
            url: this.baseURL + "userAvatar",
            filePath: tempFilePaths,
            name: "file",
            header: {
              Authorization: this.token,
            },
            success: (uploadFileRes) => {
              let data = JSON.parse(uploadFileRes.data);
              if (data.code === 200) {
                UpdateAvatar(data.data.name).then((res) => {
                  this.$u.toast("头像更换成功");
                  this.avatarSrc = this.baseURL2 + data.data.url;
                });
              } else {
                this.$u.toast(data.msg);
              }
            },
            fail: (err) => {
              this.$u.toast("头像更换失败");
            },
          });
        },
      });
    },
    openPage(path, type) {
      if (!path) return;
      let url = !!type ? path + "?majorType=" + type : path;
      uni.navigateTo({
        url: url,
      });
    },
    personalPage(path) {
      if (!path) return;
      uni.navigateTo({
        url:
          path +
          "?baseInfo=" +
          encodeURIComponent(JSON.stringify(this.baseInfo)),
      });
    },
    isJSON(str) {
      try {
        var obj = JSON.parse(str);
        if (typeof obj == "object" && obj) {
          return true;
        } else {
          return false;
        }
      } catch (e) {
        return false;
      }
    },
    logout() {
      uni.showModal({
        title: "提示",
        content: "确定退出当前账号吗？",
        success: (res) => {
          if (res.confirm) {
            const userStore = useUserStore();
            userStore.logout().then(() => {
              uni.closeSocket();
              uni.reLaunch({
                url: "/pages/login/index",
              });
            });
            this.removeAccount();
          }
        },
      });
    },
    removeAccount() {
      let model = uni.getStorageSync("rememberAccount");
      if (!model.remember) {
        model.account = "";
        model.password = "";
        model.remember = false;
        uni.setStorageSync("rememberAccount", model);
      }
    },

    showQrCode() {
      uni.navigateTo({
        url: "/pages/flow-up/myQrCode",
      });
    },
    scanCode() {
      uni.scanCode({
        success: (res) => {
          let url = "";
          if (this.isJSON(res.result.trim())) {
            const result = JSON.parse(res.result.trim());
            if (result.t === "ADP") {
              let config = {
                isPreview: 1,
                moduleId: result.id,
                previewType: result.previewType,
              };
              url =
                "/pages/apply/dynamicModel/index?config=" +
                this.xunda.base64.encode(JSON.stringify(config));
            }
            if (result.t === "DFD") {
              url =
                "/pages/apply/dynamicModel/scanForm?config=" +
                JSON.stringify(result);
            }
            if (result.t === "WFP") {
              url =
                "/pages/workFlow/scanForm/index?config=" +
                JSON.stringify(result);
            }
            if (result.t === "report") {
              let url_ = `${this.report}/preview.html?id=${result.id}&token=${this.token}&page=1&from=menu`;
              url =
                "/pages/apply/externalLink/index?url=" +
                encodeURIComponent(url_) +
                "&fullName= " +
                result.fullName;
            }
            if (result.t === "portal") {
              url = "/pages/portal/scanPortal/index?id=" + result.id;
            }
            if (result.t === "login") {
              url = "/pages/login/scanLogin?id=" + result.id;
            }
            if (result.t === "bindPhysician") {
              url = "/pages/flow-up/physician/bindPhysician?id=" + result.id;
            }
          } else {
            url = "/pages/my/scanResult/index?result=" + res.result;
          }
          uni.navigateTo({
            url,
            fail: (err) => {
              this.$u.toast("暂无此页面");
            },
          });
        },
      });
    },
  },
};
</script>

<style lang="scss">
page {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.my-container {
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 40rpx;
}

// 用户信息卡片
.user-info-card {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 12rpx 40rpx rgba(25, 118, 210, 0.3);
  color: #fff;

  .user-header {
    display: flex;
    align-items: center;
    gap: 24rpx;

    .avatar-container {
      position: relative;
      width: 120rpx;
      height: 120rpx;

      .user-avatar {
        width: 100%;
        height: 100%;
      }

      .camera-icon {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 40rpx;
        height: 40rpx;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2rpx solid #fff;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

        .icon-ym {
          color: #fff;
          font-size: 24rpx;
        }
      }
    }

    .user-details {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      gap: 12rpx;

      .user-name {
        font-size: 36rpx;
        font-weight: 700;
        color: #fff;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
      }

      .user-role {
        font-size: 28rpx;
        color: rgba(255, 255, 255, 0.9);
      }

      .arrow-icon {
        align-self: flex-end;
      }
    }
  }
}

// 菜单区域
.menu-sections {
  .menu-section {
    background: #fff;
    border-radius: 20rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(25, 118, 210, 0.08);
    border: 1rpx solid rgba(25, 118, 210, 0.1);

    .section-header {
      background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 100%);
      padding: 24rpx 28rpx;
      display: flex;
      align-items: center;
      gap: 16rpx;
      border-bottom: 1rpx solid rgba(25, 118, 210, 0.1);

      .section-icon {
        font-size: 32rpx;
      }

      .section-title {
        font-size: 32rpx;
        font-weight: 700;
        color: #1565c0;
      }
    }

    .section-content {
      padding: 28rpx;

      // 网格菜单
      .menu-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20rpx;

        .menu-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12rpx;
          padding: 16rpx 8rpx;
          border-radius: 16rpx;
          transition: all 0.3s ease;

          &:active {
            background: #f0f7ff;
            transform: scale(0.95);
          }

          .menu-icon {
            width: 80rpx;
            height: 80rpx;
            border-radius: 16rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);

            .icon-ym {
              color: #fff;
              font-size: 40rpx;
            }

            &.bg-blue {
              background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            }

            &.bg-green {
              background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            }

            &.bg-purple {
              background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
            }

            &.bg-orange {
              background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            }
          }

          .menu-text {
            font-size: 24rpx;
            color: #333;
            text-align: center;
          }
        }
      }

      // 列表项样式
      :deep(.u-cell) {
        height: 112rpx;
        padding: 0 20rpx;

        .u-cell__title-text {
          font-size: 28rpx;
          color: #333;
        }
      }
    }
  }
}

// 退出登录区域
.logout-section {
  padding: 0 20rpx;

  .logout-btn {
    background: #fff;
    border-radius: 20rpx;
    height: 108rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 32rpx rgba(25, 118, 210, 0.08);
    border: 1rpx solid rgba(25, 118, 210, 0.1);
    transition: all 0.3s ease;

    .logout-text {
      font-size: 32rpx;
      color: #f44336;
      font-weight: 600;
    }

    &.button-hover {
      transform: scale(0.98);
      background: #f5f5f5;
    }
  }
}

// 颜色类
.my-list {
  &.icon-ym-zuzhi {
    color: #6071f5;
  }

  &.icon-ym-position1 {
    color: #2b7ff0;
  }

  &.icon-ym-generator-section {
    color: #f4a02f;
  }

  &.icon-ym-shezhi {
    color: #4cbf2a;
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .my-container {
    padding: 16rpx;
  }

  .user-info-card {
    padding: 32rpx 24rpx;

    .user-header {
      gap: 16rpx;

      .avatar-container {
        width: 100rpx;
        height: 100rpx;
      }

      .user-details {
        .user-name {
          font-size: 32rpx;
        }

        .user-role {
          font-size: 26rpx;
        }
      }
    }
  }

  .menu-sections {
    .menu-section {
      .section-header {
        padding: 20rpx 24rpx;

        .section-icon {
          font-size: 28rpx;
        }

        .section-title {
          font-size: 28rpx;
        }
      }

      .section-content {
        padding: 20rpx;

        .menu-grid {
          gap: 16rpx;

          .menu-item {
            padding: 12rpx 4rpx;

            .menu-icon {
              width: 64rpx;
              height: 64rpx;

              .icon-ym {
                font-size: 32rpx;
              }
            }

            .menu-text {
              font-size: 22rpx;
            }
          }
        }
      }
    }
  }
}
</style>