<template>
  <view class="video-player-container">
    <web-view v-if="videoUrl" :src="getVideoPageUrl()"></web-view>
    <view v-else class="no-video">暂无视频内容</view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      videoUrl: '',
    };
  },
  async onLoad(options) {
    if (options.videoUrl) {
      this.videoUrl = decodeURIComponent(options.videoUrl);
    }
  },
  methods: {
    getVideoPageUrl() {
      // 创建一个只包含视频播放器的简单HTML页面URL
      if (this.videoUrl) {
        return `data:text/html;charset=utf-8,${encodeURIComponent(`
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
            <title>视频播放</title>
            <style>
              body {
                margin: 0;
                padding: 0;
                background: #000;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                overflow: hidden;
              }
              video {
                width: 100%;
                height: 100%;
                object-fit: contain;
              }
            </style>
          </head>
          <body>
            <video controls autoplay>
              <source src="${this.videoUrl}" type="video/mp4">
              您的浏览器不支持视频播放。
            </video>
          </body>
          </html>
        `)}`;
      }
      return '';
    }
  }
};
</script>

<style lang="scss">
.video-player-container {
  width: 100%;
  height: 100vh;
  background: #000;
  
  .no-video {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #fff;
    font-size: 28rpx;
  }
}
</style>