import request from "@/utils/request";

// 基础Api
export const patientApi = '/api/flowUp/patient';

// 获取列表
export function getList(data) {
    return request({
        url: patientApi + `/getList`,
        method: "post",
        data: data,
    });
}
// 新建
export function create(data) {
    return request({
        url: patientApi,
        method: "post",
        data: data,
    });
}
// 修改
export function update(data) {
    return request({
        url: patientApi + `/` + data.id,
        method: "put",
        data: data,
    });
}
// 详情(无转换数据)
export function getInfo(id) {
    return request({
        url: patientApi + `/` + id,
        method: "get",
    });
}
// 获取(转换数据)
export function getDetailInfo(id) {
    return request({
        url: patientApi + `/detail/` + id,
        method: "get",
    });
}
// 删除
export function del(id) {
    return request({
        url: patientApi + `/` + id,
        method: "delete",
    });
}
// 批量删除数据
export function batchDelete(data) {
    return request({
        url: patientApi + `/batchRemove`,
        method: "delete",
        data: data,
    });
}
// 导出
export function exportData(data) {
    return request({
        url: patientApi + `/export`,
        method: "post",
        data: data,
    });
}

// 导入模板下载
export function templateDownload() {
    return request({
        url: patientApi + `/TemplateDownload`,
        method: "get"
    });
}
// 导入
export function importData(data) {
    return request({
        url: patientApi + `/importData`,
        method: "post",
        data: data,
    });
}
// 导入预览
export function importPreview(data) {
    return request({
        url: patientApi + `/importPreview`,
        method: "get",
        data: data,
    });
}
// 导出错误数据
export function exportExceptionData(data) {
    return request({
        url: patientApi + `/exportExceptionData`,
        method: "post",
        data: data,
    });
}

export function getCurrentUserPatient() {
    return request({
        url: patientApi + `/getCurrentUserPatient`,
        method: "get",
    });
}




/**
 * 列表显示配置
 */
export const columns = [
    {
        lable: '主键',
        prop: 'id',
    },
    {
        lable: '姓名',
        prop: 'name',
    },
    {
        lable: '年龄',
        prop: 'age',
    },
    {
        lable: '性别',
        prop: 'sex',
    },
    {
        lable: '身份证',
        prop: 'idCard',
    },
    {
        lable: '住院号',
        prop: 'admissionNo',
    },
    {
        lable: '详细地址',
        prop: 'addressDetail',
    },
    {
        lable: '出院日期',
        prop: 'dischargeDate',
    },
    {
        lable: '入院日期',
        prop: 'admissionDate',
    },
    {
        lable: '入院诊断',
        prop: 'admittingDiagnosis',
    },
    {
        lable: '管床医师',
        prop: 'pipeBedPhysician',
    },
    {
        lable: '随访总人数',
        prop: 'tnumber',
    },
    {
        lable: '随访应完成人数',
        prop: 'dnumber',
    },
    {
        lable: '随访率',
        prop: 'rate',
    },
    {
        lable: '随访次数',
        prop: 'fCount',
    },
    {
        lable: '随访类型',
        prop: 'type',
    },
    {
        lable: '地址',
        prop: 'address',
    },
    {
        lable: '纬度',
        prop: 'longitude',
    },
    {
        lable: '经度',
        prop: 'latitude',
    },
]