<template>
  <view class="search-container">
    <view class="search-box">
      <input class="search-input" v-model="searchKeyword" placeholder="请输入搜索关键词" @confirm="searchNews" />
      <!-- 清除按钮 -->
      <view v-if="searchKeyword" class="clear-button" @click="clearSearch">
        <text class="clear-icon">✕</text>
      </view>
      <view class="search-button" @click="searchNews">
        <text class="search-text">搜索</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "BitSearch",
  data() {
    return {
      searchKeyword: ''
    };
  },
  methods: {
    searchNews() {
      this.$emit('search', this.searchKeyword);
    },
    clearSearch() {
      this.searchKeyword = '';
      this.$emit('search', this.searchKeyword);
    }
  }
};
</script>

<style lang="scss">
.search-container {
  .search-box {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 40rpx;
    padding: 10rpx 20rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);

    .search-input {
      flex: 1;
      height: 60rpx;
      font-size: 28rpx;
      padding: 0 20rpx;
    }

    .search-icon {
      color: #999;
      font-size: 24rpx;
      margin-right: 10rpx;
    }

    .clear-button {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f0f0f0;
      border-radius: 50%;

      .clear-icon {
        color: #999;
        font-size: 24rpx;
      }
    }

    .search-button {
      min-width: 100rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 30rpx;
      padding: 0 20rpx;

      .search-text {
        color: #000000;
        font-size: 28rpx;
        font-weight: 500;
      }
    }

  }
}
</style>