<template>
    <view class="patient-list-container">
        <!-- 美化的顶部筛选区域 -->
        <view class="filter-header-fixed">
            <view class="header-content">
                <!-- 搜索框 -->
                <view class="search-section">
                    <view class="search-box">
                        <text class="search-icon">🔍</text>
                        <input class="search-input" v-model="quickSearch" placeholder="快速搜索患者姓名..."
                            @input="handleQuickSearch" />
                        <text class="clear-icon" v-if="quickSearch" @click="clearQuickSearch">✕</text>
                    </view>
                </view>

                <!-- 筛选触发器 -->
                <view class="filter-trigger" @click="toggleFilter">
                    <view class="filter-content">
                        <text class="filter-icon">🎛️</text>
                        <text class="filter-text">高级筛选</text>
                        <text class="filter-arrow" :class="{ 'filter-arrow-up': showFilter }">▼</text>
                    </view>
                    <view class="filter-badge" v-if="hasActiveFilters">{{ activeFilterCount }}</view>
                </view>
            </view>
        </view>

        <!-- 筛选面板 -->
        <view class="filter-panel" v-show="showFilter" @touchmove.stop.prevent>
            <!-- 遮罩层 -->
            <view class="filter-mask" @click="closeFilter"></view>

            <!-- 筛选内容 -->
            <view class="filter-content" @touchmove.stop>
                <view class="filter-form-container">
                    <scroll-view scroll-y="true" class="filter-scroll">
                        <u-form :label-width="150" class="filter-form">
                            <view class="form-section">
                                <view class="section-title">基本信息</view>
                                <u-form-item label="姓名">
                                    <u-input v-model="searchForm.name" placeholder="请输入患者姓名" class="uni-input"
                                        type="text" :clearable="true" />
                                </u-form-item>
                                <u-form-item label="性别">
                                    <picker :value="getSexIndex()" :range="getSexOptions()" range-key="fullName"
                                        @change="onSexChange">
                                        <view class="uni-picker">
                                            {{ getSexText() || "请选择性别" }}
                                        </view>
                                    </picker>
                                </u-form-item>
                                <u-form-item label="身份证">
                                    <u-input v-model="searchForm.idCard" placeholder="请输入身份证号" class="uni-input"
                                        type="idcard" :clearable="true" />
                                </u-form-item>
                            </view>

                            <view class="form-section">
                                <view class="section-title">住院信息</view>
                                <u-form-item label="住院号">
                                    <u-input v-model="searchForm.admissionNo" placeholder="请输入住院号" class="uni-input"
                                        type="text" :clearable="true" />
                                </u-form-item>
                                <u-form-item label="出院日期">
                                    <uni-datetime-picker type="date" v-model="searchForm.dischargeDate" />
                                </u-form-item>
                            </view>

                            <view class="form-section">
                                <view class="section-title">随访信息</view>
                                <u-form-item label="随访类型">
                                    <picker :value="getTypeIndex()" :range="getTypeOptions()" range-key="fullName"
                                        @change="onTypeChange">
                                        <view class="uni-picker">
                                            {{ getTypeText() || "请选择随访类型" }}
                                        </view>
                                    </picker>
                                </u-form-item>
                            </view>
                        </u-form>
                    </scroll-view>
                </view>

                <view class="filter-actions">
                    <u-button class="action-btn reset-btn" @click="reset">
                        重置
                    </u-button>
                    <u-button class="action-btn search-btn" type="primary" @click="search">
                        检索
                    </u-button>
                </view>
            </view>
        </view>
        <!-- 患者列表区域 -->
        <view class="patient-list-wrapper" :class="{ 'list-disabled': showFilter }">
            <mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption"
                :fixed="false">
                <view class="patient-list-content">
                    <!-- 统计信息卡片 -->
                    <view class="stats-overview" v-if="list.length > 0">
                        <view class="stats-card">
                            <view class="stats-item">
                                <view class="stats-number">{{ list.length }}</view>
                                <view class="stats-label">总患者</view>
                            </view>
                            <view class="stats-divider"></view>
                            <view class="stats-item">
                                <view class="stats-number">{{ maleCount }}</view>
                                <view class="stats-label">男性</view>
                            </view>
                            <view class="stats-divider"></view>
                            <view class="stats-item">
                                <view class="stats-number">{{ femaleCount }}</view>
                                <view class="stats-label">女性</view>
                            </view>
                        </view>
                    </view>

                    <!-- 患者列表 -->
                    <uni-swipe-action ref="swipeAction">
                        <uni-transition v-for="(item, index) in list" :key="index" mode-class="fade" :duration="300"
                            :delay="index * 50" :show="true">
                            <uni-swipe-action-item :threshold="0" :right-options="options" class="patient-item-wrapper">
                                <view class="patient-card" @click="goDetail(item)">
                                    <!-- 患者头像和基本信息 -->
                                    <view class="patient-header">
                                        <view class="patient-avatar" :class="getGenderClass(item.sex)">
                                            <text class="avatar-text">{{ getAvatarText(item.name) }}</text>
                                            <view class="avatar-ring"></view>
                                        </view>
                                        <view class="patient-basic-info">
                                            <view class="patient-name">
                                                <text class="name-text">{{ item.name }}</text>
                                                <view class="patient-meta">
                                                    <view class="age-badge">{{ item.age }}岁</view>
                                                    <view class="gender-badge" :class="getGenderClass(item.sex)">
                                                        {{ getSexText(item.sex) }}
                                                    </view>
                                                </view>
                                            </view>
                                            <view class="patient-id">
                                                <text class="id-icon">🆔</text>
                                                <text class="id-value">{{ formatIdCard(item.idCard) }}</text>
                                            </view>
                                        </view>
                                        <view class="patient-actions">
                                            <view class="action-dot" @click.stop="showActionMenu(item, index)">
                                                <text class="dot-icon">⋯</text>
                                            </view>
                                        </view>
                                    </view>

                                    <!-- 住院信息 -->
                                    <view class="hospital-info">
                                        <view class="info-row">
                                            <view class="info-item">
                                                <text class="info-label">住院号:</text>
                                                <text class="info-value">{{
                                                    item.admissionNo || "暂无"
                                                }}</text>
                                            </view>
                                            <view class="info-item" v-if="item.dischargeDate">
                                                <text class="info-label">出院:</text>
                                                <text class="info-value">{{
                                                    formatDate(item.dischargeDate)
                                                }}</text>
                                            </view>
                                        </view>
                                        <view class="info-row" v-if="item.addressDetail">
                                            <view class="info-item address-item">
                                                <text class="info-label">地址:</text>
                                                <text class="info-value address-text">{{
                                                    item.addressDetail
                                                }}</text>
                                            </view>
                                        </view>
                                    </view>
                                </view>

                                <!-- 滑动操作按钮 -->
                                <template v-slot:right>
                                    <view class="swipe-actions">
                                        <view class="action-btn follow-btn" @click.stop="startFollow(item)">
                                            <text class="action-text">随访</text>
                                        </view>
                                        <view class="action-btn delete-btn" @click.stop="handleClick(index)">
                                            <text class="action-text">删除</text>
                                        </view>
                                    </view>
                                </template>
                            </uni-swipe-action-item>
                        </uni-transition>
                    </uni-swipe-action>

                    <!-- 空状态 -->
                    <view class="empty-state" v-if="list.length === 0 && !loading">
                        <uni-transition mode-class="fade" :show="true" :duration="500">
                            <view class="empty-content">
                                <view class="empty-icon">👥</view>
                                <text class="empty-text">暂无患者数据</text>
                                <text class="empty-desc">请添加患者信息</text>
                                <view class="empty-action">
                                    <view class="refresh-btn" @click="refreshList">
                                        <text class="refresh-icon">🔄</text>
                                        刷新列表
                                    </view>
                                </view>
                            </view>
                        </uni-transition>
                    </view>
                </view>
            </mescroll-uni>
        </view>
    </view>
</template>
<script>
import resources from "@/libs/resources.js";
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import { getDictionaryDataSelector } from "@/api/common";
// import { useBaseStore } from "@/store/modules/base";
// const baseStore = useBaseStore();
// import request from "@/utils/request";

import { getList, del, columns } from "@/api/flow-up/patient";
export default {
    mixins: [MescrollMixin],
    components: {},
    data() {
        return {
            isAuthority: true,
            icon: resources.message.nodata,
            searchForm: {},
            showFilter: false,
            upOption: {
                page: {
                    num: 0,
                    size: 20,
                },
                empty: {
                    use: true,
                    icon: resources.message.nodata,
                    tip: "暂无患者数据\n点击筛选条件查找患者",
                    fixed: true,
                    zIndex: 5,
                },
                textNoMore: "已显示全部患者",
                textLoading: "正在加载患者信息...",
                toTop: {
                    src: "/static/image/to-top.png",
                    bottom: 120,
                    width: 72,
                    radius: "50%",
                },
            },
            list: [],
            columns: columns,
            listQuery: {
                moduleId: "***********",
                sidx: "",
                keyword: "",
                json: "",
            },
            options: [
                {
                    text: "删除",
                    style: {
                        backgroundColor: "#dd524d",
                    },
                },
            ],
            sortOptions: [
                {
                    label: "主键降序",
                    sidx: "id",
                    value: "-id",
                    sort: "desc",
                },
                {
                    label: "主键升序",
                    sidx: "id",
                    value: "id",
                    sort: "asc",
                },
                {
                    label: "姓名降序",
                    sidx: "name",
                    value: "-name",
                    sort: "desc",
                },
                {
                    label: "姓名升序",
                    sidx: "name",
                    value: "name",
                    sort: "asc",
                },

                {
                    label: "年龄降序",
                    sidx: "age",
                    value: "-age",
                    sort: "desc",
                },
                {
                    label: "年龄升序",
                    sidx: "age",
                    value: "age",
                    sort: "asc",
                },

                {
                    label: "性别降序",
                    sidx: "sex",
                    value: "-sex",
                    sort: "desc",
                },
                {
                    label: "性别升序",
                    sidx: "sex",
                    value: "sex",
                    sort: "asc",
                },

                {
                    label: "身份证降序",
                    sidx: "idCard",
                    value: "-idCard",
                    sort: "desc",
                },
                {
                    label: "身份证升序",
                    sidx: "idCard",
                    value: "idCard",
                    sort: "asc",
                },

                {
                    label: "住院号降序",
                    sidx: "admissionNo",
                    value: "-admissionNo",
                    sort: "desc",
                },
                {
                    label: "住院号升序",
                    sidx: "admissionNo",
                    value: "admissionNo",
                    sort: "asc",
                },

                {
                    label: "详细地址降序",
                    sidx: "addressDetail",
                    value: "-addressDetail",
                    sort: "desc",
                },
                {
                    label: "详细地址升序",
                    sidx: "addressDetail",
                    value: "addressDetail",
                    sort: "asc",
                },

                {
                    label: "出院日期降序",
                    sidx: "dischargeDate",
                    value: "-dischargeDate",
                    sort: "desc",
                },
                {
                    label: "出院日期升序",
                    sidx: "dischargeDate",
                    value: "dischargeDate",
                    sort: "asc",
                },

                {
                    label: "入院日期降序",
                    sidx: "admissionDate",
                    value: "-admissionDate",
                    sort: "desc",
                },
                {
                    label: "入院日期升序",
                    sidx: "admissionDate",
                    value: "admissionDate",
                    sort: "asc",
                },

                {
                    label: "入院诊断降序",
                    sidx: "admittingDiagnosis",
                    value: "-admittingDiagnosis",
                    sort: "desc",
                },
                {
                    label: "入院诊断升序",
                    sidx: "admittingDiagnosis",
                    value: "admittingDiagnosis",
                    sort: "asc",
                },

                {
                    label: "管床医师降序",
                    sidx: "pipeBedPhysician",
                    value: "-pipeBedPhysician",
                    sort: "desc",
                },
                {
                    label: "管床医师升序",
                    sidx: "pipeBedPhysician",
                    value: "pipeBedPhysician",
                    sort: "asc",
                },

                {
                    label: "随访总人数降序",
                    sidx: "tnumber",
                    value: "-tnumber",
                    sort: "desc",
                },
                {
                    label: "随访总人数升序",
                    sidx: "tnumber",
                    value: "tnumber",
                    sort: "asc",
                },

                {
                    label: "随访应完成人数降序",
                    sidx: "dnumber",
                    value: "-dnumber",
                    sort: "desc",
                },
                {
                    label: "随访应完成人数升序",
                    sidx: "dnumber",
                    value: "dnumber",
                    sort: "asc",
                },

                {
                    label: "随访率降序",
                    sidx: "rate",
                    value: "-rate",
                    sort: "desc",
                },
                {
                    label: "随访率升序",
                    sidx: "rate",
                    value: "rate",
                    sort: "asc",
                },

                {
                    label: "随访次数降序",
                    sidx: "fCount",
                    value: "-fCount",
                    sort: "desc",
                },
                {
                    label: "随访次数升序",
                    sidx: "fCount",
                    value: "fCount",
                    sort: "asc",
                },

                {
                    label: "随访类型降序",
                    sidx: "type",
                    value: "-type",
                    sort: "desc",
                },
                {
                    label: "随访类型升序",
                    sidx: "type",
                    value: "type",
                    sort: "asc",
                },

                {
                    label: "地址降序",
                    sidx: "address",
                    value: "-address",
                    sort: "desc",
                },
                {
                    label: "地址升序",
                    sidx: "address",
                    value: "address",
                    sort: "asc",
                },

                {
                    label: "纬度降序",
                    sidx: "longitude",
                    value: "-longitude",
                    sort: "desc",
                },
                {
                    label: "纬度升序",
                    sidx: "longitude",
                    value: "longitude",
                    sort: "asc",
                },

                {
                    label: "经度降序",
                    sidx: "latitude",
                    value: "-latitude",
                    sort: "desc",
                },
                {
                    label: "经度升序",
                    sidx: "latitude",
                    value: "latitude",
                    sort: "asc",
                },
            ],
            ableAll: {},
            menuId: "",
            columnList: [],
            dataValue: {},
            userInfo: {},
            firstInitSearchData: false,
            tabList: [],
            tabKey: 0,
            optionsObj: {
                defaultProps: {
                    label: "fullName",
                    value: "enCode",
                    multiple: false,
                    children: "",
                },
            },
            // 新增属性
            quickSearch: "",
            loading: false,
            searchTimer: null,
        };
    },

    computed: {
        // 男性患者数量
        maleCount() {
            return this.list.filter(item => item.sex === '1' || item.sex === 1).length;
        },
        // 女性患者数量
        femaleCount() {
            return this.list.filter(item => item.sex === '2' || item.sex === 2).length;
        },
        // 是否有激活的筛选条件
        hasActiveFilters() {
            return Object.values(this.searchForm).some(value =>
                value !== undefined && value !== null && value !== ''
            );
        },
        // 激活的筛选条件数量
        activeFilterCount() {
            return Object.values(this.searchForm).filter(value =>
                value !== undefined && value !== null && value !== ''
            ).length;
        }
    },
    onLoad(e) {
        this.userInfo = uni.getStorageSync("userInfo") || {};
        this.menuId = e.menuId;
        this.setDefaultQuery();
        this.dataAll();
        this.getColumnList();
    },
    onShow() {
        this.$nextTick(() => {
            this.mescroll.resetUpScroll();
        });
    },
    onUnload() {
        uni.$off("refresh");
    },
    methods: {
        // 快速搜索处理
        handleQuickSearch() {
            clearTimeout(this.searchTimer);
            this.searchTimer = setTimeout(() => {
                this.searchForm.name = this.quickSearch;
                this.search();
            }, 500);
        },

        // 清除快速搜索
        clearQuickSearch() {
            this.quickSearch = "";
            this.searchForm.name = "";
            this.search();
        },

        // 获取性别样式类
        getGenderClass(sex) {
            if (sex === '1' || sex === 1) return 'male';
            if (sex === '2' || sex === 2) return 'female';
            return 'unknown';
        },

        // 获取性别文本
        getSexText(sex) {
            if (sex === '1' || sex === 1) return '男';
            if (sex === '2' || sex === 2) return '女';
            return '未知';
        },

        // 显示操作菜单
        showActionMenu(item, index) {
            uni.showActionSheet({
                itemList: ['查看详情', '开始随访', '编辑信息', '删除患者'],
                success: (res) => {
                    switch (res.tapIndex) {
                        case 0:
                            this.goDetail(item);
                            break;
                        case 1:
                            this.startFollow(item);
                            break;
                        case 2:
                            this.editPatient(item);
                            break;
                        case 3:
                            this.confirmDelete(item, index);
                            break;
                    }
                }
            });
        },

        // 编辑患者
        editPatient(item) {
            uni.navigateTo({
                url: `/pages/flow-up/patient/edit?id=${item.id}`
            });
        },

        // 确认删除
        confirmDelete(item, index) {
            uni.showModal({
                title: '确认删除',
                content: `确定要删除患者 ${item.name} 吗？`,
                success: (res) => {
                    if (res.confirm) {
                        this.handleClick(index);
                    }
                }
            });
        },

        // 刷新列表
        refreshList() {
            this.list = [];
            this.mescroll.resetUpScroll();
        },

        getSelectOptions() {
            getDictionaryDataSelector("Sex").then((res) => {
                this.optionsObj.SexOptions = res.data.list;
            });
            getDictionaryDataSelector("FlowType").then((res) => {
                this.optionsObj.FlowTypeOptions = res.data.list;
            });
        },

        // 性别选择器相关方法
        getSexOptions() {
            return this.optionsObj.SexOptions || [];
        },
        getSexIndex() {
            if (!this.searchForm.sex) return 0;
            const options = this.getSexOptions();
            const index = options.findIndex(
                (item) => item.enCode === this.searchForm.sex
            );
            return index >= 0 ? index : 0;
        },
        getSexText() {
            if (!this.searchForm.sex) return "";
            const options = this.getSexOptions();
            const item = options.find((item) => item.enCode === this.searchForm.sex);
            return item ? item.fullName : "";
        },
        onSexChange(e) {
            const index = e.detail.value;
            const options = this.getSexOptions();
            if (options[index]) {
                this.searchForm.sex = options[index].enCode;
            }
        },

        // 随访类型选择器相关方法
        getTypeOptions() {
            return this.optionsObj.FlowTypeOptions || [];
        },
        getTypeIndex() {
            if (!this.searchForm.type) return 0;
            const options = this.getTypeOptions();
            const index = options.findIndex(
                (item) => item.enCode === this.searchForm.type
            );
            return index >= 0 ? index : 0;
        },
        getTypeText() {
            if (!this.searchForm.type) return "";
            const options = this.getTypeOptions();
            const item = options.find((item) => item.enCode === this.searchForm.type);
            return item ? item.fullName : "";
        },
        onTypeChange(e) {
            const index = e.detail.value;
            const options = this.getTypeOptions();
            if (options[index]) {
                this.searchForm.type = options[index].enCode;
            }
        },
        dataAll() {
            this.getSelectOptions();
        },
        // 切换筛选面板显示状态
        toggleFilter() {
            this.showFilter = !this.showFilter;
        },

        // 关闭筛选面板
        closeFilter() {
            this.showFilter = false;
        },
        //设置默认排序
        setDefaultQuery() {
            const defaultSortConfig = [];
            const sortField = defaultSortConfig.map(
                (o) => (o.sort === "desc" ? "-" : "") + o.field
            );
            this.listQuery.sidx = sortField.join(",");
        },
        //初始化查询的默认数据
        async initSearchData() {
            this.dataValue = JSON.parse(JSON.stringify(this.searchForm));
        },
        // 上拉刷新
        async upCallback(page) {
            if (!this.firstInitSearchData) {
                await this.initSearchData();
                this.firstInitSearchData = true;
            }
            const query = {
                currentPage: page.num,
                pageSize: page.size,
                menuId: this.menuId,
                ...this.listQuery,
                ...this.searchForm,
                dataType: 0,
                patientFlag: true,
            };
            getList(query)
                .then((res) => {
                    let _list = res.data.list;
                    this.mescroll.endSuccess(_list.length);
                    if (page.num == 1) this.list = [];
                    // const list = _list.map((o) => ({
                    //   show: false,
                    //   ...o,
                    // }));
                    this.list = this.list.concat(_list);
                })
                .catch(() => {
                    this.mescroll.endSuccess(this.list.length);
                });
        },
        handleClick(index) {
            const item = this.list[index];
            uni.showModal({
                title: "提示",
                content: "确定删除?",
                success: (res) => {
                    if (res.confirm) {
                        this.delete(item);
                    }
                },
            });
        },
        // 删除
        delete(item) {
            del(item.id).then((res) => {
                uni.showToast({
                    title: res.msg,
                    complete: () => {
                        this.$u.toast(res.msg);
                        this.mescroll.resetUpScroll();
                    },
                });
            });
        },
        open(index) {
            this.list[index].show = true;
            this.list.forEach((_, idx) => {
                if (index != idx) this.list[idx].show = false;
            });
        },
        search() {
            // 关闭筛选面板
            this.showFilter = false;

            if (this.isPreview == "1") return;
            this.searchTimer && clearTimeout(this.searchTimer);
            this.searchTimer = setTimeout(() => {
                this.list = [];
                this.mescroll.resetUpScroll();
            }, 300);
        },
        // 跳转详情页
        goDetail(item) {
            let id = item.id;
            // let btnType = "";
            let btnList = [];
            btnList.push("btn_edit");
            btnList.push("btn_detail");
            if (btnList.length == 0) return;
            this.jumPage(id, btnList);
        },
        // 跳转添加页
        addPage() {
            this.jumPage();
        },
        jumPage(id, btnList) {
            let idVal = id ? "&id=" + id : "";
            let idList = [];
            for (let i = 0; i < this.list.length; i++) {
                idList.push(this.list[i].id);
            }
            let idListVal = "&idList=" + idList;
            if (!id) {
                uni.navigateTo({
                    url: "./form?menuId=" + this.menuId + "&jurisdictionType=btn_add",
                });
            } else if (btnList.includes("btn_detail")) {
                uni.navigateTo({
                    url:
                        "./detail?menuId=" +
                        this.menuId +
                        "&btnList=" +
                        btnList +
                        idVal +
                        idListVal,
                });
            } else if (btnList.includes("btn_edit")) {
                uni.navigateTo({
                    url:
                        "./form?menuId=" +
                        this.menuId +
                        "&jurisdictionType=btn_edit&btnList=" +
                        btnList +
                        idVal +
                        idListVal,
                });
            }
        },
        // 权限列
        getColumnList() {
            let columnPermissionList = [];
            let _appColumnList = this.columns;
            for (let i = 0; i < _appColumnList.length; i++) {
                columnPermissionList.push(_appColumnList[i]);
            }
            this.columnList = columnPermissionList;
        },
        // 重置查询条件
        reset() {
            this.searchForm = {};
            // 重置后自动执行搜索
            // this.search();
        },
        // 查询检索（保留兼容性）
        closeDropdown() {
            this.search();
        },
        dataList(data) {
            let _list = data.list;
            return _list;
        },

        // 获取头像文字
        getAvatarText(name) {
            if (!name) return "患";
            return name.length > 1 ? name.slice(-2) : name;
        },

        // 格式化身份证号
        formatIdCard(idCard) {
            if (!idCard) return "暂无";
            if (idCard.length <= 8) return idCard;
            return idCard.slice(0, 4) + "****" + idCard.slice(-4);
        },

        // 格式化日期
        formatDate(dateStr) {
            if (!dateStr) return "暂无";
            const date = new Date(dateStr);
            const month = date.getMonth() + 1;
            const day = date.getDate();
            return `${month}月${day}日`;
        },

        // 获取随访状态
        getFollowStatusText(item) {
            if (item.fCount > 0) {
                return "已随访";
            }
            return "待随访";
        },

        // 获取随访状态样式
        getFollowStatusClass(item) {
            if (item.fCount > 0) {
                return "status-completed";
            }
            return "status-pending";
        },

        // 获取随访类型文本
        getFollowTypeText(type) {
            // 这里可以根据实际的类型映射来处理
            return type || "常规随访";
        },

        // 开始随访
        startFollow(item) {
            uni.showModal({
                title: "随访确认",
                content: `确定要对患者 ${item.name} 进行随访吗？`,
                success: (res) => {
                    if (res.confirm) {
                        // 这里可以跳转到随访页面或执行随访逻辑
                        uni.showToast({
                            title: "开始随访",
                            icon: "success",
                        });
                    }
                },
            });
        },
    },
};
</script>

<style lang="scss">
page {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    min-height: 100vh;
}

.patient-list-container {
    min-height: 100vh;
    background: transparent;
    display: flex;
    flex-direction: column;
    position: relative;
}

// 美化的顶部筛选区域样式
.filter-header-fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 24rpx 20rpx;
    box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
    z-index: 100;

    .header-content {
        display: flex;
        flex-direction: column;
        gap: 20rpx;

        // 搜索区域
        .search-section {
            .search-box {
                display: flex;
                align-items: center;
                background: rgba(255, 255, 255, 0.95);
                border-radius: 50rpx;
                padding: 0 24rpx;
                backdrop-filter: blur(10rpx);
                box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

                .search-icon {
                    font-size: 28rpx;
                    color: #666;
                    margin-right: 16rpx;
                }

                .search-input {
                    flex: 1;
                    height: 80rpx;
                    font-size: 28rpx;
                    color: #333;
                    border: none;
                    outline: none;
                    background: transparent;

                    &::placeholder {
                        color: #999;
                    }
                }

                .clear-icon {
                    font-size: 24rpx;
                    color: #999;
                    padding: 8rpx;
                    margin-left: 8rpx;
                    border-radius: 50%;
                    background: #f0f0f0;
                    transition: all 0.3s ease;

                    &:active {
                        background: #e0e0e0;
                        transform: scale(0.9);
                    }
                }
            }
        }

        // 筛选触发器
        .filter-trigger {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 16rpx 32rpx;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 40rpx;
            border: 2rpx solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10rpx);
            transition: all 0.3s ease;

            &:active {
                transform: scale(0.98);
                background: rgba(255, 255, 255, 0.3);
            }

            .filter-content {
                display: flex;
                align-items: center;
                gap: 12rpx;

                .filter-icon {
                    font-size: 24rpx;
                    filter: grayscale(1) brightness(10);
                }

                .filter-text {
                    font-size: 26rpx;
                    color: #fff;
                    font-weight: 600;
                }

                .filter-arrow {
                    font-size: 20rpx;
                    color: rgba(255, 255, 255, 0.8);
                    transition: transform 0.3s ease;

                    &.filter-arrow-up {
                        transform: rotate(180deg);
                    }
                }
            }

            .filter-badge {
                position: absolute;
                top: -8rpx;
                right: -8rpx;
                min-width: 32rpx;
                height: 32rpx;
                background: #ff4757;
                color: #fff;
                font-size: 20rpx;
                font-weight: 600;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.4);
            }
        }
    }
}

// 筛选面板样式
.filter-panel {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;

    .filter-mask {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        z-index: 1000;
    }

    .filter-content {
        position: absolute;
        top: 0rpx;
        /* 从固定头部下方开始 */
        left: 0;
        right: 0;
        bottom: 0;
        background: #fff;
        z-index: 1001;
        display: flex;
        flex-direction: column;
        box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);

        .filter-form-container {
            flex: 1;
            overflow: hidden;

            .filter-scroll {
                height: 100%;
                padding: 24rpx 24rpx;

                .filter-form {
                    margin-right: 24rpx;

                    .form-section {
                        margin-bottom: 3rpx;

                        .section-title {
                            font-size: 28rpx;
                            font-weight: 600;
                            color: #34495e;
                            margin-bottom: 20rpx;
                            padding: 12rpx 16rpx;
                            border-left: 6rpx solid #3498db;
                            background: #f8f9fa;
                            border-radius: 8rpx;
                        }
                    }
                }
            }
        }

        .filter-actions {
            display: flex;
            gap: 20rpx;
            padding: 24rpx;
            background: #f8f9fa;
            border-top: 1rpx solid #e9ecef;

            .action-btn {
                flex: 1;
                height: 80rpx;
                border-radius: 40rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 500;

                &.reset-btn {
                    background: #f8f9fa;
                    border: 2rpx solid #dee2e6;
                    color: #6c757d;
                }

                &.search-btn {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border: none;
                    color: #fff;
                }
            }
        }
    }
}

// 统计概览样式
.stats-overview {
    margin-bottom: 32rpx;

    .stats-card {
        display: flex;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20rpx;
        padding: 32rpx 24rpx;
        backdrop-filter: blur(10rpx);
        box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.1);
        border: 2rpx solid rgba(102, 126, 234, 0.1);

        .stats-item {
            flex: 1;
            text-align: center;

            .stats-number {
                font-size: 40rpx;
                font-weight: 700;
                color: #667eea;
                margin-bottom: 8rpx;
            }

            .stats-label {
                font-size: 24rpx;
                color: #666;
                font-weight: 500;
            }
        }

        .stats-divider {
            width: 2rpx;
            background: linear-gradient(to bottom, transparent, #e0e0e0, transparent);
            margin: 0 20rpx;
        }
    }
}

// 患者列表样式
.patient-list-wrapper {
    flex: 1;
    padding: 200rpx 24rpx 24rpx;
    /* 顶部留出固定头部的空间 */
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;

    &.list-disabled {
        pointer-events: none;
        opacity: 0.5;
    }

    .patient-list-content {
        .patient-item-wrapper {
            margin-bottom: 32rpx;
            /* 增加患者卡片间距 */
            border-radius: 24rpx;
            /* 更大的圆角 */
            overflow: hidden;
            box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
            background: #fff;
            transition: all 0.3s ease;

            /* 添加渐变边框效果 */
            position: relative;

            &::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 6rpx;
                background: linear-gradient(90deg,
                        #667eea 0%,
                        #764ba2 50%,
                        #f093fb 100%);
                border-radius: 24rpx 24rpx 0 0;
            }

            &:hover {
                transform: translateY(-8rpx);
                box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
            }

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

// 患者卡片样式
.patient-card {
    background: #fff;
    padding: 32rpx 28rpx;
    position: relative;
    z-index: 2;
    border-radius: 20rpx;
    box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.08);
    border: 2rpx solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;

    // 添加渐变装饰条
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6rpx;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    &:active {
        transform: translateY(-4rpx) scale(0.98);
        box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.15);
    }

    margin-top: 6rpx;
    /* 为渐变边框留出空间 */

    .patient-header {
        display: flex;
        align-items: flex-start;
        margin-bottom: 28rpx;

        .patient-avatar {
            position: relative;
            width: 96rpx;
            height: 96rpx;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 24rpx;
            box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
            overflow: hidden;

            // 性别主题色
            &.male {
                background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
            }

            &.female {
                background: linear-gradient(135deg, #f48fb1 0%, #ec407a 100%);
            }

            &.unknown {
                background: linear-gradient(135deg, #bdbdbd 0%, #9e9e9e 100%);
            }

            .avatar-text {
                color: #fff;
                font-size: 32rpx;
                font-weight: 700;
                text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
            }

            .avatar-ring {
                position: absolute;
                top: -4rpx;
                left: -4rpx;
                right: -4rpx;
                bottom: -4rpx;
                border: 3rpx solid rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                animation: pulse 2s infinite;
            }
        }

        .patient-basic-info {
            flex: 1;
            min-width: 0;

            .patient-name {
                margin-bottom: 16rpx;

                .name-text {
                    font-size: 36rpx;
                    font-weight: 700;
                    color: #2c3e50;
                    margin-bottom: 8rpx;
                    line-height: 1.3;
                }

                .patient-meta {
                    display: flex;
                    align-items: center;
                    gap: 12rpx;

                    .age-badge {
                        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
                        color: #fff;
                        padding: 8rpx 16rpx;
                        border-radius: 20rpx;
                        font-size: 22rpx;
                        font-weight: 600;
                        box-shadow: 0 4rpx 12rpx rgba(243, 156, 18, 0.3);
                    }

                    .gender-badge {
                        padding: 8rpx 16rpx;
                        border-radius: 20rpx;
                        font-size: 22rpx;
                        font-weight: 600;

                        &.male {
                            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
                            color: #1976d2;
                            border: 2rpx solid rgba(25, 118, 210, 0.2);
                        }

                        &.female {
                            background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
                            color: #c2185b;
                            border: 2rpx solid rgba(194, 24, 91, 0.2);
                        }

                        &.unknown {
                            background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%);
                            color: #757575;
                            border: 2rpx solid rgba(117, 117, 117, 0.2);
                        }
                    }
                }
            }

            .patient-id {
                display: flex;
                align-items: center;
                gap: 8rpx;

                .id-icon {
                    font-size: 20rpx;
                    color: #7f8c8d;
                }

                .id-value {
                    font-size: 24rpx;
                    color: #34495e;
                    font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
                    font-weight: 500;
                }
            }
        }

        .patient-actions {
            display: flex;
            align-items: center;

            .action-dot {
                width: 60rpx;
                height: 60rpx;
                background: rgba(102, 126, 234, 0.1);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;

                .dot-icon {
                    font-size: 24rpx;
                    color: #667eea;
                    font-weight: 700;
                }

                &:active {
                    transform: scale(0.9);
                    background: rgba(102, 126, 234, 0.2);
                }
            }
        }

        .follow-status {
            .status-badge {
                padding: 10rpx 20rpx;
                /* 增加内边距 */
                border-radius: 24rpx;
                /* 更大圆角 */
                font-size: 24rpx;
                /* 增大字体 */
                font-weight: 600;
                box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
                /* 添加阴影 */

                &.status-completed {
                    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
                    color: #155724;
                    border: 2rpx solid #b8dacc;
                }

                &.status-pending {
                    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
                    color: #856404;
                    border: 2rpx solid #f1c40f;
                }
            }
        }
    }

    // 住院信息样式
    .hospital-info {
        margin-bottom: 24rpx;
        /* 增加间距 */
        padding: 20rpx;
        /* 添加内边距 */
        background: #f8f9fa;
        /* 添加背景色 */
        border-radius: 16rpx;
        /* 添加圆角 */
        border: 1rpx solid #e9ecef;
        /* 添加边框 */

        .info-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20rpx;
            /* 增加间距 */
            margin-bottom: 16rpx;
            /* 增加间距 */

            &:last-child {
                margin-bottom: 0;
            }

            .info-item {
                display: flex;
                align-items: center;
                flex: 1;
                min-width: 0;
                padding: 8rpx 12rpx;
                /* 添加内边距 */
                background: #fff;
                /* 添加背景色 */
                border-radius: 12rpx;
                /* 添加圆角 */
                border: 1rpx solid #dee2e6;
                /* 添加边框 */

                &.address-item {
                    flex: 100%;
                }

                .info-label {
                    font-size: 26rpx;
                    /* 增大字体 */
                    color: #6c757d;
                    margin-right: 12rpx;
                    /* 增加间距 */
                    white-space: nowrap;
                    font-weight: 600;
                }

                .info-value {
                    font-size: 26rpx;
                    /* 增大字体 */
                    color: #495057;
                    flex: 1;
                    min-width: 0;
                    font-weight: 500;

                    &.address-text {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
            }
        }
    }

    // 随访信息样式
    .follow-info {
        border-top: 2rpx solid #e9ecef;
        /* 加粗分割线 */
        padding-top: 20rpx;
        /* 增加间距 */
        margin-top: 8rpx;
        /* 添加上边距 */

        .follow-stats {
            display: flex;
            gap: 32rpx;
            /* 增加间距 */

            .stat-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                flex: 1;
                padding: 16rpx 12rpx;
                /* 添加内边距 */
                background: linear-gradient(135deg,
                        #f8f9fa 0%,
                        #e9ecef 100%);
                /* 添加背景渐变 */
                border-radius: 16rpx;
                /* 添加圆角 */
                border: 1rpx solid #dee2e6;
                /* 添加边框 */
                box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
                /* 添加阴影 */

                .stat-number {
                    font-size: 32rpx;
                    /* 增大字体 */
                    font-weight: 700;
                    color: #3498db;
                    margin-bottom: 8rpx;
                    /* 增加间距 */
                    text-shadow: 0 2rpx 4rpx rgba(52, 152, 219, 0.2);
                    /* 添加文字阴影 */
                }

                .stat-label {
                    font-size: 24rpx;
                    /* 增大字体 */
                    color: #6c757d;
                    text-align: center;
                    font-weight: 500;
                }
            }
        }
    }
}

// 滑动操作按钮样式
.swipe-actions {
    display: flex;
    height: 100%;

    .action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 140rpx;
        /* 增加宽度 */
        color: #fff;
        font-weight: 600;
        position: relative;
        overflow: hidden;

        .action-text {
            font-size: 26rpx;
            /* 增大字体 */
            text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
            /* 添加文字阴影 */
        }

        &.follow-btn {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            box-shadow: inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
            /* 添加内阴影 */

            &:active {
                background: linear-gradient(135deg, #229954 0%, #27ae60 100%);
            }
        }

        &.delete-btn {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            box-shadow: inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
            /* 添加内阴影 */

            &:active {
                background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
            }
        }
    }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
    .filter-header-fixed {
        padding: 16rpx;

        .filter-trigger {
            padding: 16rpx 20rpx;

            .filter-text {
                font-size: 28rpx;
            }

            .filter-arrow {
                font-size: 22rpx;
            }
        }
    }

    .patient-list-wrapper {
        padding: 140rpx 16rpx 16rpx;
        /* 调整移动端的顶部间距 */

        .patient-list-content {
            .patient-item-wrapper {
                margin-bottom: 24rpx;
                /* 减少移动端间距 */
            }
        }

        .patient-card {
            padding: 24rpx 20rpx;
            /* 减少移动端内边距 */

            .patient-header {
                .patient-avatar {
                    width: 80rpx;
                    /* 移动端稍小的头像 */
                    height: 80rpx;

                    .avatar-text {
                        font-size: 28rpx;
                    }
                }

                .patient-basic-info {
                    .patient-name {
                        .name-text {
                            font-size: 32rpx;
                        }

                        .age-badge {
                            font-size: 22rpx;
                            padding: 4rpx 12rpx;
                        }
                    }

                    .patient-id {

                        .id-label,
                        .id-value {
                            font-size: 24rpx;
                        }
                    }
                }
            }

            .hospital-info {
                padding: 16rpx;

                .info-row {
                    gap: 16rpx;

                    .info-item {

                        .info-label,
                        .info-value {
                            font-size: 24rpx;
                        }
                    }
                }
            }

            .follow-info {
                .follow-stats {
                    gap: 20rpx;

                    .stat-item {
                        padding: 12rpx 8rpx;

                        .stat-number {
                            font-size: 28rpx;
                        }

                        .stat-label {
                            font-size: 22rpx;
                        }
                    }
                }
            }
        }
    }

    .swipe-actions {
        .action-btn {
            width: 120rpx;

            .action-text {
                font-size: 24rpx;
            }
        }
    }
}

// 自定义下拉刷新样式
::v-deep .mescroll-downwarp {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    border-radius: 0 0 20rpx 20rpx;

    .mescroll-downwarp-content {
        font-size: 28rpx;
        font-weight: 500;
    }
}

// 自定义上拉加载样式
::v-deep .mescroll-upwarp {
    .mescroll-upwarp-tip {
        color: #7f8c8d;
        font-size: 26rpx;
    }
}

// 空状态样式
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 40rpx;
    text-align: center;

    .empty-content {
        display: flex;
        flex-direction: column;
        align-items: center;

        .empty-icon {
            font-size: 120rpx;
            color: #e0e0e0;
            margin-bottom: 32rpx;
            opacity: 0.8;
        }

        .empty-text {
            font-size: 32rpx;
            color: #666;
            font-weight: 600;
            margin-bottom: 16rpx;
        }

        .empty-desc {
            font-size: 26rpx;
            color: #999;
            line-height: 1.5;
            margin-bottom: 40rpx;
        }

        .empty-action {
            .refresh-btn {
                padding: 20rpx 40rpx;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: #fff;
                border-radius: 40rpx;
                font-size: 26rpx;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 12rpx;
                box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
                transition: all 0.3s ease;

                .refresh-icon {
                    font-size: 24rpx;
                }

                &:active {
                    transform: scale(0.95);
                    box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
                }
            }
        }
    }
}

// 动画定义
@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }

    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }

    100% {
        opacity: 1;
        transform: scale(1);
    }
}

// 确保患者列表区域可以正常点击
.patient-list-wrapper {
    pointer-events: auto;

    .patient-card {
        pointer-events: auto;
        cursor: pointer;
    }
}
</style>
