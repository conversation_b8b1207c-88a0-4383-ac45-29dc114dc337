<template>
    <view class="empty-state" :style="computedStyle">
        <uni-transition v-if="useTransition" mode-class="fade" :show="show" :duration="duration">
            <view class="empty-content">
                <view class="empty-icon">{{ icon }}</view>
                <text class="empty-text">{{ text }}</text>
                <text v-if="desc" class="empty-desc">{{ desc }}</text>
                <view v-if="$slots.action" class="empty-action">
                    <slot name="action" />
                </view>
            </view>
        </uni-transition>
        <view v-else class="empty-content">
            <view class="empty-icon">{{ icon }}</view>
            <text class="empty-text">{{ text }}</text>
            <text v-if="desc" class="empty-desc">{{ desc }}</text>
            <view v-if="$slots.action" class="empty-action">
                <slot name="action" />
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "EmptyState",
    props: {
        show: {
            type: Boolean,
            default: true,
        },
        icon: {
            type: String,
            default: "📋",
        },
        text: {
            type: String,
            default: "暂无数据",
        },
        desc: {
            type: String,
            default: "",
        },
        padding: {
            type: [Number, String],
            default: 120, // rpx
        },
        useTransition: {
            type: Boolean,
            default: true,
        },
        duration: {
            type: Number,
            default: 500,
        },
        alignCenter: {
            type: Boolean,
            default: true,
        },
    },
    computed: {
        computedStyle() {
            const paddingVal = typeof this.padding === "number" ? `${this.padding}rpx` : this.padding;
            return {
                padding: `${paddingVal} 40rpx`,
                textAlign: this.alignCenter ? "center" : "left",
            };
        },
    },
};
</script>

<style lang="scss" scoped>
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .empty-content {
        display: flex;
        flex-direction: column;
        align-items: center;

        .empty-icon {
            font-size: 120rpx;
            color: #e0e0e0;
            margin-bottom: 32rpx;
            opacity: 0.8;
        }

        .empty-text {
            font-size: 32rpx;
            color: #666;
            font-weight: 600;
            margin-bottom: 16rpx;
        }

        .empty-desc {
            font-size: 26rpx;
            color: #999;
            line-height: 1.5;
            margin-bottom: 40rpx;
        }
    }
}
</style>


