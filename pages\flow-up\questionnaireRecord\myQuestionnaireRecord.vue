<template>
    <view class="questionnaire-record-list-container">
        <!-- 顶部搜索和筛选区域 -->
        <view class="header-section">
            <!-- 使用BitSearch组件替换原来的搜索框 -->
            <BitSearch @search="handleSearch" />

            <!-- 统计信息卡片 -->
            <view class="stats-card flowup-card" v-if="list.length > 0">
                <view class="stats-item">
                    <view class="stats-number">{{ list.length }}</view>
                    <view class="stats-label">总问卷</view>
                </view>
                <view class="stats-divider"></view>
                <view class="stats-item">
                    <view class="stats-number">{{ completedCount }}</view>
                    <view class="stats-label">已完成</view>
                </view>
                <view class="stats-divider"></view>
                <view class="stats-item">
                    <view class="stats-number">{{ pendingCount }}</view>
                    <view class="stats-label">待填写</view>
                </view>
            </view>
        </view>

        <!-- 列表内容 -->
        <view class="list-section">
            <mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption"
                :fixed="false">
                <view class="record-list">
                    <!-- 添加过渡动画 -->
                    <uni-transition v-for="(item, index) in list" :key="index" mode-class="fade" :duration="300"
                        :delay="index * 50" :show="true">
                        <view class="record-card flowup-card" @click="goDetail(item)">
                            <view class="card-header">
                                <view class="patient-info">
                                    <!-- 添加问卷图标 -->
                                    <view class="questionnaire-icon">
                                        <text class="icon-questionnaire">📋</text>
                                    </view>
                                    <view class="questionnaire-details">
                                        <view class="questionnaire-title">{{
                                            item.questionnaireTitle || "未命名问卷"
                                        }}</view>
                                        <view class="questionnaire-desc">
                                            <text class="time-icon">🕒</text>
                                            {{ item.questionnaireTime }}
                                        </view>
                                        <!-- 添加患者信息 -->
                                        <view class="patient-name" v-if="item.patientName">
                                            <text class="patient-icon">👤</text>
                                            {{ item.patientName }}
                                        </view>
                                    </view>
                                </view>
                                <view class="record-status flowup-status-badge" :class="getStatusClass(item)">
                                    <view class="status-icon" :class="getStatusIconClass(item)"></view>
                                    {{ getStatusText(item) }}
                                </view>
                            </view>

                            <!-- 添加操作按钮 -->
                            <view class="card-actions" v-if="!item.isAnswered">
                                <view class="action-btn primary-btn flowup-action-btn"
                                    @click.stop="fillQuestionnaire(item)">
                                    <text class="btn-icon">✏️</text>
                                    立即填写
                                </view>
                            </view>
                            <view class="card-actions" v-else>
                                <view class="action-btn secondary-btn flowup-action-btn" @click.stop="viewDetail(item)">
                                    <text class="btn-icon">👁</text>
                                    查看详情
                                </view>
                            </view>
                        </view>
                    </uni-transition>

                    <!-- 空状态 -->
                    <view class="empty-state" v-if="list.length === 0 && !loading">
                        <uni-transition mode-class="fade" :show="true" :duration="500">
                            <view class="empty-content">
                                <view class="empty-icon">📋</view>
                                <text class="empty-text">暂无问卷记录</text>
                                <text class="empty-desc">完成问卷后将在此显示</text>
                                <view class="empty-action">
                                    <view class="refresh-btn flowup-action-btn primary-btn" @click="refreshList">
                                        <text class="refresh-icon">🔄</text>
                                        刷新列表
                                    </view>
                                </view>
                            </view>
                        </uni-transition>
                    </view>
                </view>
            </mescroll-uni>
        </view>
    </view>
</template>

<script>
import resources from "@/libs/resources.js";
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import { getList } from "@/api/flow-up/questionnaireRecord";
import BitSearch from "@/components/gzbit/BitSearch.vue";

export default {
    mixins: [MescrollMixin],
    components: {
        BitSearch,
    },
    data() {
        return {
            isAuthority: true,
            icon: resources.message.nodata,
            searchForm: {
                name: undefined,
            },
            downOption: {
                use: true,
                auto: false,
            },
            dataOptions: {},
            upOption: {
                page: {
                    num: 0,
                    size: 10,
                    time: null,
                },
                empty: {
                    use: true,
                    icon: resources.message.nodata,
                    tip: "暂无数据",
                    fixed: true,
                    zIndex: 5,
                },
                textNoMore: "没有更多数据",
                toTop: {
                    bottom: 250,
                },
            },
            list: [],
            appColumnList: [
                {
                    prop: "questionnaireTitle",
                    label: "问卷标题",
                },
                {
                    prop: "patientName",
                    label: "问卷患者",
                    width: 100,
                    align: "center",
                    sort: true,
                },
                {
                    prop: "isAnswered",
                    label: "是否填写",
                    width: 100,
                    align: "center",
                    sort: true,
                },
            ],
            listQuery: {
                moduleId: "661876504619124229",
                sidx: "",
                keyword: "",
                json: "",
            },
            options: [],
            ableAll: {},
            interfaceRes: {
                name: [],
            },
            menuId: "",
            columnList: [],
            key: new Date(),
            dataValue: {},
            userInfo: {},
            firstInitSearchData: false,
            tabList: [],
            tabKey: 0,
            optionsObj: {
                defaultProps: {
                    label: "fullName",
                    value: "enCode",
                    multiple: false,
                    children: "",
                },
            },
            loading: false,
        };
    },
    computed: {
        // 已完成问卷数量
        completedCount() {
            return this.list.filter(item => item.isAnswered).length;
        },
        // 待填写问卷数量
        pendingCount() {
            return this.list.filter(item => !item.isAnswered).length;
        }
    },
    onLoad(e) {
        this.userInfo = uni.getStorageSync("userInfo") || {};
        this.menuId = e.menuId;
        this.setDefaultQuery();
    },
    onShow() {
        this.$nextTick(() => {
            this.mescroll.resetUpScroll();
        });
    },
    onUnload() {
        uni.$off("refresh");
    },
    methods: {
        // 获取状态文本
        getStatusText(item) {
            if (item.isAnswered) return "已填写";
            return "未填写";
        },
        // 获取状态样式类
        getStatusClass(item) {
            if (item.isAnswered) return "status-completed";
            return "status-pending";
        },

        // 获取状态图标样式类
        getStatusIconClass(item) {
            if (item.isAnswered) return "icon-completed";
            return "icon-pending";
        },

        // 填写问卷
        fillQuestionnaire(item) {
            this.goDetail(item);
        },

        // 查看详情
        viewDetail(item) {
            this.goDetail(item);
        },

        // 刷新列表
        refreshList() {
            this.list = [];
            this.mescroll.resetUpScroll();
        },

        // 滚动到顶部
        scrollToTop() {
            this.mescroll.scrollTo(0, 300);
        },

        // 清空搜索
        clearSearch() {
            this.listQuery.keyword = "";
            this.search();
        },

        // 添加handleSearch方法处理BitSearch组件的搜索事件
        handleSearch(keyword) {
            this.listQuery.keyword = keyword;
            this.search();
        },

        openData() { },
        setDefaultQuery() {
            const defaultSortConfig = [];
            const sortField = defaultSortConfig.map(
                (o) => (o.sort === "desc" ? "-" : "") + o.field
            );
            this.listQuery.sidx = sortField.join(",");
        },
        //初始化查询的默认数据
        async initSearchData() {
            this.dataValue = JSON.parse(JSON.stringify(this.searchForm));
        },

        async upCallback(page) {
            if (!this.firstInitSearchData) {
                await this.initSearchData();
                this.firstInitSearchData = true;
            }
            const query = {
                currentPage: page.num,
                pageSize: page.size,
                menuId: this.menuId,
                ...this.listQuery,
                ...this.searchForm,
                dataType: 0,
                myFlag: true,
            };
            getList(query)
                .then((res) => {
                    let _list = res.data.list;
                    this.mescroll.endSuccess(_list.length);
                    if (page.num == 1) this.list = [];
                    this.list = this.list.concat(_list);
                })
                .catch(() => {
                    this.mescroll.endSuccess(this.list.length);
                });
        },
        search() {
            if (this.isPreview == "1") return;
            this.searchTimer && clearTimeout(this.searchTimer);
            this.searchTimer = setTimeout(() => {
                this.list = [];
                this.mescroll.resetUpScroll();
            }, 300);
        },
        goDetail(item) {
            let id = item.id;
            let btnList = [];
            btnList.push("btn_detail");
            if (!item.isAnswered) {
                btnList.push("btn_fill_out");
            }
            if (btnList.length == 0) return;
            let idVal = id ? "&id=" + id : "";
            uni.navigateTo({
                url:
                    "./doQuestionnaireRecord?menuId=" +
                    this.menuId +
                    "&btnList=" +
                    btnList +
                    idVal
            });
        },
    },
};
</script>

<style lang="scss">
page {
    background-color: #f0f2f6;
    height: 100%;
    /* #ifdef MP-ALIPAY */
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    /* #endif */
}

.questionnaire-record-list-container {
    min-height: 100%;
    background-color: #f0f2f6;
    padding-bottom: 20rpx;

    // 头部区域
    .header-section {
        position: sticky;
        top: 0;
        left: 0;
        right: 0;
        z-index: 9999;
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        padding: 24rpx 20rpx;
        box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);

        // 统计卡片
        .stats-card {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20rpx;
            padding: 24rpx;
            margin-top: 20rpx;
            backdrop-filter: blur(10rpx);
            box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

            .stats-item {
                flex: 1;
                text-align: center;

                .stats-number {
                    font-size: 36rpx;
                    font-weight: 700;
                    color: #1976d2;
                    margin-bottom: 8rpx;
                }

                .stats-label {
                    font-size: 24rpx;
                    color: #666;
                    font-weight: 500;
                }
            }

            .stats-divider {
                width: 2rpx;
                background: linear-gradient(to bottom, transparent, #e0e0e0, transparent);
                margin: 0 20rpx;
            }
        }
    }

    // 列表区域
    .list-section {
        padding: 24rpx 24rpx;

        .record-list {

            // 记录卡片
            .record-card {
                background: #fff;
                border-radius: 20rpx;
                padding: 32rpx;
                margin-bottom: 24rpx;
                box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.08);
                border: 2rpx solid rgba(102, 126, 234, 0.1);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;

                // 添加渐变背景装饰
                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 6rpx;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                }

                &:active {
                    transform: translateY(-4rpx) scale(0.98);
                    box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.15);
                }

                .card-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 24rpx;

                    .patient-info {
                        display: flex;
                        align-items: flex-start;
                        gap: 20rpx;
                        flex: 1;

                        .questionnaire-icon {
                            width: 80rpx;
                            height: 80rpx;
                            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
                            border-radius: 20rpx;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.3);

                            .icon-questionnaire {
                                font-size: 36rpx;
                                filter: grayscale(1) brightness(10);
                            }
                        }

                        .questionnaire-details {
                            flex: 1;

                            .questionnaire-title {
                                font-size: 32rpx;
                                color: #333;
                                font-weight: 600;
                                margin-bottom: 12rpx;
                                line-height: 1.4;
                            }

                            .questionnaire-desc {
                                font-size: 26rpx;
                                color: #666;
                                margin-bottom: 8rpx;
                                display: flex;
                                align-items: center;
                                gap: 8rpx;

                                .time-icon {
                                    font-size: 24rpx;
                                }
                            }

                            .patient-name {
                                font-size: 24rpx;
                                color: #999;
                                display: flex;
                                align-items: center;
                                gap: 8rpx;

                                .patient-icon {
                                    font-size: 22rpx;
                                }
                            }
                        }
                    }

                    .record-status {
                        padding: 12rpx 20rpx;
                        border-radius: 40rpx;
                        font-size: 24rpx;
                        font-weight: 600;
                        display: flex;
                        align-items: center;
                        gap: 8rpx;
                        white-space: nowrap;

                        .status-icon {
                            width: 12rpx;
                            height: 12rpx;
                            border-radius: 50%;

                            &.icon-completed {
                                background: #4caf50;
                                box-shadow: 0 0 0 4rpx rgba(76, 175, 80, 0.2);
                            }

                            &.icon-pending {
                                background: #ff9800;
                                box-shadow: 0 0 0 4rpx rgba(255, 152, 0, 0.2);
                            }
                        }

                        &.status-completed {
                            background: #e8f5e9;
                            color: #4caf50;
                        }

                        &.status-pending {
                            background: #ffebee;
                            color: #f44336;
                        }
                    }
                }

                // 操作按钮
                .card-actions {
                    display: flex;
                    justify-content: flex-end;
                    margin-top: 20rpx;

                    .action-btn {
                        padding: 16rpx 32rpx;
                        border-radius: 40rpx;
                        font-size: 26rpx;
                        font-weight: 600;
                        display: flex;
                        align-items: center;
                        gap: 8rpx;
                        transition: all 0.3s ease;

                        .btn-icon {
                            font-size: 24rpx;
                        }

                        &.primary {
                            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
                            color: #fff;
                            box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.3);

                            &:active {
                                transform: scale(0.95);
                                box-shadow: 0 2rpx 8rpx rgba(25, 118, 210, 0.3);
                            }
                        }

                        &.secondary {
                            background: rgba(25, 118, 210, 0.1);
                            color: #667eea;
                            border: 2rpx solid rgba(25, 118, 210, 0.2);

                            &:active {
                                transform: scale(0.95);
                                background: rgba(25, 118, 210, 0.15);
                            }
                        }
                    }
                }
            }

            // 空状态
            .empty-state {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 120rpx 40rpx;
                text-align: center;

                .empty-content {
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    .empty-icon {
                        font-size: 120rpx;
                        color: #e0e0e0;
                        margin-bottom: 32rpx;
                        opacity: 0.8;
                    }

                    .empty-text {
                        font-size: 32rpx;
                        color: #666;
                        font-weight: 600;
                        margin-bottom: 16rpx;
                    }

                    .empty-desc {
                        font-size: 26rpx;
                        color: #999;
                        line-height: 1.5;
                        margin-bottom: 40rpx;
                    }

                    .empty-action {
                        .refresh-btn {
                            padding: 20rpx 40rpx;
                            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
                            color: #fff;
                            border-radius: 40rpx;
                            font-size: 26rpx;
                            font-weight: 600;
                            display: flex;
                            align-items: center;
                            gap: 12rpx;
                            box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.3);
                            transition: all 0.3s ease;

                            .refresh-icon {
                                font-size: 24rpx;
                            }

                            &:active {
                                transform: scale(0.95);
                                box-shadow: 0 2rpx 8rpx rgba(25, 118, 210, 0.3);
                            }
                        }
                    }
                }
            }
        }
    }

    // 浮动操作按钮
    .fab-container {
        position: fixed;
        bottom: 120rpx;
        right: 40rpx;
        z-index: 999;

        .fab-btn {
            width: 96rpx;
            height: 96rpx;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8rpx 32rpx rgba(25, 118, 210, 0.4);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

            .fab-icon {
                font-size: 32rpx;
                filter: grayscale(1) brightness(10);
            }

            &:active {
                transform: scale(0.9);
                box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.4);
            }
        }
    }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
    .header-section {
        padding: 16rpx;

        .search-card {
            padding: 12rpx 16rpx;

            .search-input-wrapper {
                gap: 12rpx;

                .search-icon {
                    font-size: 28rpx;
                }

                .search-input {
                    font-size: 26rpx;
                }
            }
        }
    }

    .list-section {
        padding: 16rpx;

        .record-list {
            .record-card {
                padding: 20rpx;

                .card-header {
                    .patient-info {
                        gap: 12rpx;

                        .questionnaire-details {
                            .questionnaire-title {
                                font-size: 28rpx;
                            }

                            .questionnaire-desc {
                                font-size: 22rpx;
                            }
                        }
                    }

                    .record-status {
                        padding: 6rpx 12rpx;
                        font-size: 20rpx;
                    }
                }
            }
        }
    }
}
</style>
