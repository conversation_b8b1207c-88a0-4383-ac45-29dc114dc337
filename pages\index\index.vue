<template>
  <view class="index-container">
    <!-- 加载状态 -->
    <view class="loading-overlay" v-if="isLoading">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载...</text>
      </view>
    </view>

    <!-- 角色切换动画容器 -->
    <uni-transition mode-class="fade" :show="!isLoading" :duration="500">
      <view class="role-container">
        <!-- 患者界面 -->
        <uni-transition mode-class="slide-left" :show="userRole === '患者'" :duration="300">
          <view v-if="userRole === '患者'" class="role-content">
            <patient ref="patientRef" />
          </view>
        </uni-transition>

        <!-- 医师界面 -->
        <uni-transition mode-class="slide-right" :show="userRole === '医师'" :duration="300">
          <view v-if="userRole === '医师'" class="role-content">
            <physician />
          </view>
        </uni-transition>

        <!-- 未知角色或未登录状态 -->
        <uni-transition mode-class="fade" :show="!userRole && !isLoading" :duration="300">
          <view v-if="!userRole && !isLoading" class="welcome-container">
            <view class="welcome-content">
              <view class="welcome-icon">🏥</view>
              <view class="welcome-title">欢迎使用</view>
              <view class="welcome-desc">医疗随访管理系统</view>
              <view class="welcome-actions">
                <view class="action-btn primary" @click="handleLogin">
                  <text class="btn-icon">👤</text>
                  立即登录
                </view>
              </view>
            </view>
          </view>
        </uni-transition>
      </view>
    </uni-transition>
  </view>

</template>
<script>
var wv; //计划创建的webview
import logoImg from "@/static/logo.png";
import { getMyPhysicianAsync, scanBindAsync } from "@/api/flow-up/physician";
// #ifndef MP
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import IndexMixin from "./mixin.js";
// #endif
import patient from "@/pages/index/patient.vue";
import physician from "@/pages/index/physician.vue";
export default {
  // #ifndef MP
  mixins: [MescrollMixin, IndexMixin],
  // #endif
  components: {
    patient,
    physician,
  },
  data() {
    return {
      logoImg,
      gridItems: [],
      myPhysician: null,
      avatar: "",
      userRole: "",
      isLoading: true,
      roleTransition: false,
    };
  },
  methods: {
    navigateTo(pagePath) {
      uni.navigateTo({
        url: pagePath,
      });
    },

    // 调用子组件方法示例
    refreshPatientInfo() {
      // 先判断组件是否存在
      if (this.$refs.patientRef) {
        this.$refs.patientRef.loadMyInfo();
      }
    },

    // 处理登录
    handleLogin() {
      uni.navigateTo({
        url: '/pages/login/index'
      });
    },

    // 初始化用户角色
    async initUserRole() {
      this.isLoading = true;
      try {
        // 模拟加载延迟
        await new Promise(resolve => setTimeout(resolve, 800));

        const userInfo = uni.getStorageSync("userInfo") || {};
        const roleName = userInfo.roleName || "";

        // 添加角色切换动画
        if (this.userRole !== roleName) {
          this.roleTransition = true;
          setTimeout(() => {
            this.userRole = roleName;
            this.roleTransition = false;
          }, 150);
        } else {
          this.userRole = roleName;
        }

        // 如果是患者角色，刷新患者信息
        if (this.userRole === "患者") {
          this.$nextTick(() => {
            this.refreshPatientInfo();
          });
        }
      } catch (error) {
        console.error('初始化用户角色失败:', error);
      } finally {
        this.isLoading = false;
      }
    },
  },
  onShow() {
    this.initUserRole();
  },

  onReady() {
    // 页面准备完成
  },

  onLoad(e) {
    // 页面加载
  },
  computed: {},
};
</script>

<style lang="scss">
.index-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  position: relative;
  overflow: hidden;

  // 加载覆盖层
  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10rpx);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;

    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 24rpx;

      .loading-spinner {
        width: 80rpx;
        height: 80rpx;
        border: 6rpx solid #e0e0e0;
        border-top: 6rpx solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .loading-text {
        font-size: 28rpx;
        color: #666;
        font-weight: 500;
      }
    }
  }

  // 角色容器
  .role-container {
    min-height: 100vh;
    position: relative;

    .role-content {
      min-height: 100vh;
    }
  }

  // 欢迎界面
  .welcome-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 40rpx;

    .welcome-content {
      text-align: center;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 32rpx;
      padding: 80rpx 60rpx;
      box-shadow: 0 20rpx 60rpx rgba(102, 126, 234, 0.15);
      backdrop-filter: blur(20rpx);
      border: 2rpx solid rgba(255, 255, 255, 0.2);

      .welcome-icon {
        font-size: 120rpx;
        margin-bottom: 32rpx;
        opacity: 0.9;
      }

      .welcome-title {
        font-size: 48rpx;
        font-weight: 700;
        color: #333;
        margin-bottom: 16rpx;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .welcome-desc {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 60rpx;
        line-height: 1.6;
      }

      .welcome-actions {
        .action-btn {
          display: inline-flex;
          align-items: center;
          gap: 12rpx;
          padding: 24rpx 48rpx;
          border-radius: 50rpx;
          font-size: 28rpx;
          font-weight: 600;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          &.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);

            &:active {
              transform: translateY(2rpx) scale(0.98);
              box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4);
            }
          }

          .btn-icon {
            font-size: 24rpx;
          }
        }
      }
    }
  }
}

// 动画定义
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .index-container {
    .welcome-container {
      padding: 20rpx;

      .welcome-content {
        padding: 60rpx 40rpx;

        .welcome-icon {
          font-size: 100rpx;
        }

        .welcome-title {
          font-size: 40rpx;
        }

        .welcome-desc {
          font-size: 26rpx;
        }
      }
    }

    .role-indicator {
      bottom: 40rpx;
      padding: 12rpx 24rpx;

      .indicator-dot {
        width: 12rpx;
        height: 12rpx;
      }

      .indicator-label {
        font-size: 22rpx;
      }
    }
  }
}
</style>
