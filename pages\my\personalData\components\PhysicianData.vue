<template>
  <view class="physician-form-container">
    <!-- 顶部医生信息卡片 -->
    <view class="physician-info-card">
      <view class="physician-header">
        <view class="physician-avatar" @click="selectAvatar">
          <image class="avatar-image" v-if="dataForm.picture" :src="getPhysicianAvatar()" mode="aspectFill"></image>
          <text class="avatar-text" v-else>{{ getPhysicianAvatarText() }}</text>
        </view>
        <view class="physician-details">
          <view class="physician-name">{{ dataForm.name || "未知医生" }}</view>
          <view class="physician-meta">
            <text class="meta-label">身份证号:</text>
            <text class="meta-value">{{ dataForm.idCard || "暂无" }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 表单区域 -->
    <view class="form-sections">
      <!-- 基本信息 -->
      <view class="form-section animate__animated animate__fadeInUp" :style="{ animationDelay: '0.1s' }">
        <view class="section-header" @click="toggleSection('basic')">
          <view class="section-icon">👨‍⚕️</view>
          <text class="section-title">基本信息</text>
          <view class="section-toggle">
            <text class="toggle-icon">{{ openSections.basic ? '▲' : '▼' }}</text>
          </view>
        </view>
        <view class="section-content" v-show="openSections.basic">
          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.2s' }">
            <text class="form-label required">姓名</text>
            <view class="form-field">
              <u-input v-model="dataForm.name" placeholder="请输入姓名" class="form-input" clearable border="surround" />
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.3s' }">
            <text class="form-label">性别</text>
            <view class="form-field">
              <XundaSelect v-model="dataForm.sex" placeholder="请选择" :options="genderOptions" :props="props"
                class="form-select" />
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.4s' }">
            <text class="form-label">出生日期</text>
            <view class="form-field date-field">
              <picker class="date-picker" mode="date" fields="date" :value="dataForm.birthdate"
                @change="onBirthdateChange">
                <view class="date-picker-inner">
                  <text class="date-text">{{ dataForm.birthdate ? formatDisplayDate(dataForm.birthdate) : '请选择日期'
                  }}</text>
                  <text class="arrow-icon">▼</text>
                </view>
              </picker>
              <view v-if="dataForm.birthdate" class="clear-date-btn" @click="clearBirthdate">
                <text class="clear-icon">✕</text>
              </view>
            </view>
          </view>
          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.5s' }">
            <text class="form-label">民族</text>
            <view class="form-field">
              <XundaSelect v-model="dataForm.nation" placeholder="请选择" :options="nationOptions" :props="props"
                class="form-select" />
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.6s' }">
            <text class="form-label">政治面貌</text>
            <view class="form-field">
              <XundaSelect v-model="dataForm.politicalGroup" placeholder="请选择" :options="politicalOptions"
                :props="props" class="form-select" :filterable="true" :clearBtn="true" />
            </view>
          </view>
        </view>
      </view>

      <!-- 联系信息 -->
      <view class="form-section animate__animated animate__fadeInUp" :style="{ animationDelay: '0.2s' }">
        <view class="section-header" @click="toggleSection('contact')">
          <view class="section-icon">📞</view>
          <text class="section-title">联系信息</text>
          <view class="section-toggle">
            <text class="toggle-icon">{{ openSections.contact ? '▲' : '▼' }}</text>
          </view>
        </view>
        <view class="section-content" v-show="openSections.contact">
          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.3s' }">
            <text class="form-label">联系电话</text>
            <view class="form-field">
              <u-input v-model="dataForm.phone" placeholder="请输入" class="form-input" clearable border="surround">
              </u-input>
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.4s' }">
            <text class="form-label">地址</text>
            <view class="form-field">
              <u-input v-model="dataForm.address" placeholder="请输入" class="form-input" clearable border="surround">
              </u-input>
            </view>
          </view>
        </view>
      </view>

      <!-- 职业信息 -->
      <view class="form-section animate__animated animate__fadeInUp" :style="{ animationDelay: '0.3s' }">
        <view class="section-header" @click="toggleSection('professional')">
          <view class="section-icon">🏥</view>
          <text class="section-title">职业信息</text>
          <view class="section-toggle">
            <text class="toggle-icon">{{ openSections.professional ? '▲' : '▼' }}</text>
          </view>
        </view>
        <view class="section-content" v-show="openSections.professional">
          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.4s' }">
            <text class="form-label">医院名称</text>
            <view class="form-field">
              <u-input v-model="dataForm.hospitalName" placeholder="请输入" class="form-input" clearable border="surround">
              </u-input>
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.5s' }">
            <text class="form-label">科室</text>
            <view class="form-field">
              <u-input v-model="dataForm.department" placeholder="请输入" class="form-input" clearable border="surround">
              </u-input>
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.7s' }">
            <text class="form-label">职务</text>
            <view class="form-field">
              <XundaSelect v-model="dataForm.position" placeholder="请选择" :options="positionOptions" :props="props"
                class="form-select" />
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.8s' }">
            <text class="form-label">执业级别</text>
            <view class="form-field">
              <XundaSelect v-model="dataForm.medicalLevel" placeholder="请选择" :options="medicalLevelOptions"
                :props="props" class="form-select" />
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.9s' }">
            <text class="form-label">在职状态</text>
            <view class="form-field">
              <XundaSelect v-model="dataForm.vocationalStatus" placeholder="请选择" :options="vocationalStatusOptions"
                :props="props" class="form-select" />
            </view>
          </view>
        </view>
      </view>

      <!-- 专业信息 -->
      <view class="form-section animate__animated animate__fadeInUp" :style="{ animationDelay: '0.4s' }">
        <view class="section-header" @click="toggleSection('specialty')">
          <view class="section-icon">📋</view>
          <text class="section-title">专业信息</text>
          <view class="section-toggle">
            <text class="toggle-icon">{{ openSections.specialty ? '▲' : '▼' }}</text>
          </view>
        </view>
        <view class="section-content" v-show="openSections.specialty">
          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.5s' }">
            <text class="form-label">专业领域</text>
            <view class="form-field">
              <XundaSelect v-model="dataForm.majorField" placeholder="请选择" :options="majorFieldOptions" :props="props"
                class="form-select" />
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.6s' }">
            <text class="form-label">学历</text>
            <view class="form-field">
              <XundaSelect v-model="dataForm.educationLevel" placeholder="请选择" :options="educationLevelOptions" :props="{
                label: 'fullName', value: 'fullName',
              }" class="form-select" />
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.7s' }">
            <text class="form-label">学位</text>
            <view class="form-field">
              <XundaSelect v-model="dataForm.degree" placeholder="请选择" :options="degreeOptions" :props="{
                label: 'fullName', value: 'fullName',
              }" class="form-select" />
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.8s' }">
            <text class="form-label">毕业院校</text>
            <view class="form-field">
              <u-input v-model="dataForm.graduationSchool" placeholder="请输入" class="form-input" clearable
                border="surround">
              </u-input>
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.9s' }">
            <text class="form-label">毕业时间</text>
            <view class="form-field date-field">
              <picker class="date-picker" mode="date" fields="date" :value="dataForm.graduationTime"
                @change="onGraduationTimeChange">
                <view class="date-picker-inner">
                  <text class="date-text">{{ dataForm.graduationTime ? formatDisplayDate(dataForm.graduationTime) :
                    '请选择日期'
                  }}</text>
                  <text class="arrow-icon">▼</text>
                </view>
              </picker>
              <view v-if="dataForm.graduationTime" class="clear-date-btn" @click="clearGraduationTime">
                <text class="clear-icon">✕</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 证书信息 -->
      <view class="form-section animate__animated animate__fadeInUp" :style="{ animationDelay: '0.5s' }">
        <view class="section-header" @click="toggleSection('certificate')">
          <view class="section-icon">📜</view>
          <text class="section-title">证书信息</text>
          <view class="section-toggle">
            <text class="toggle-icon">{{ openSections.certificate ? '▲' : '▼' }}</text>
          </view>
        </view>
        <view class="section-content" v-show="openSections.certificate">
          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.6s' }">
            <text class="form-label">执业证书</text>
            <view class="form-field">
              <u-input v-model="dataForm.certificateNo" placeholder="请输入证书编号" class="form-input" clearable
                border="surround">
              </u-input>
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.7s' }">
            <text class="form-label">资格证书</text>
            <view class="form-field">
              <u-input v-model="dataForm.qualificationCertificateNo" placeholder="请输入证书编号" class="form-input" clearable
                border="surround">
              </u-input>
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.8s' }">
            <text class="form-label">工作证明</text>
            <view class="form-field">
              <u-input v-model="dataForm.workCertificateNo" placeholder="请输入证书编号" class="form-input" clearable
                border="surround">
              </u-input>
            </view>
          </view>
        </view>
      </view>

      <!-- 其他信息 -->
      <view class="form-section animate__animated animate__fadeInUp" :style="{ animationDelay: '0.6s' }">
        <view class="section-header" @click="toggleSection('other')">
          <view class="section-icon">📝</view>
          <text class="section-title">其他信息</text>
          <view class="section-toggle">
            <text class="toggle-icon">{{ openSections.other ? '▲' : '▼' }}</text>
          </view>
        </view>
        <view class="section-content" v-show="openSections.other">
          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.7s' }">
            <text class="form-label">备注</text>
            <view class="form-field">
              <u-input v-model="dataForm.remarks" placeholder="请输入" type="textarea" class="form-textarea" clearable
                border="surround" auto-height />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="action-buttons">
      <view class="action-row">
        <view class="action-btn secondary-btn" @click="resetForm" hover-class="button-hover">
          <view class="btn-icon">↩️</view>
          <text class="btn-text">取消</text>
        </view>
        <view class="action-btn primary-btn" @click="submit" hover-class="button-hover">
          <view class="btn-icon">✅</view>
          <text class="btn-text">保存</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { UpdateUser } from "@/api/common";
import { update as updatePhysician } from "@/api/flow-up/physician";
import { useBaseStore } from "@/store/modules/base";
import XundaUploadImg from "@/components/Xunda/UploadImg/index.vue";
const baseStore = useBaseStore();
export default {
  components: {
    XundaUploadImg
  },
  props: {
    personalData: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    const data = {
      show: false,
      props: {
        label: "fullName",
        value: "enCode",
      },
      // 添加用于控制各部分展开/收起的状态
      openSections: {
        basic: true,
        contact: true,
        professional: true,
        specialty: true,
        certificate: true,
        other: true
      },
      dataForm: {
        id: null,
        name: "",
        picture: "",
        sex: "",
        birthdate: null,
        nation: "",
        politicalGroup: "",
        phone: "",
        address: "",
        idCard: "",
        position: "",
        medicalLevel: "",
        technicalTitle: "",
        vocationalStatus: "",
        certificateNo: "",
        qualificationCertificateNo: "",
        workCertificateNo: "",
        educationLevel: "",
        degree: "",
        graduationSchool: "",
        graduationTime: null,
        major: "",
        majorField: "",
        hospitalName: "",
        department: "",
        remarks: "",
      },
      genderOptions: [],
      nationOptions: [],
      politicalOptions: [],
      titleOptions: [],
      positionOptions: [],
      medicalLevelOptions: [],
      vocationalStatusOptions: [],
      majorFieldOptions: [],
      degreeOptions: [],
      educationLevelOptions: [],
      avatarFileList: [], // 头像文件列表
      rules: {
        name: [
          {
            required: true,
            message: "请输入姓名",
            trigger: ["change", "blur"],
          },
        ],
      },
    };
    return data;
  },
  computed: {
    baseURL() {
      return this.define.baseURL;
    },
  },
  watch: {
    personalData: {
      handler(val) {
        this.init();
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
  },
  methods: {
    // 添加日期格式化方法（用于显示）
    formatDisplayDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${year}年${month}月${day}日`;
    },

    // 获取医生头像文字
    getPhysicianAvatarText() {
      const name = this.dataForm.name;
      if (!name) return "医";
      return name.length > 1 ? name.slice(-2) : name;
    },
    getPhysicianAvatar() {
      const picture = this.dataForm.picture;
      if (picture)
        return this.define.baseURL + picture;
    },

    init() {
      let initData = JSON.parse(JSON.stringify(this.personalData));
      for (let key in initData) {
        for (let k in this.dataForm) {
          if (key === k) {
            this.dataForm[key] = initData[key];
          }
        }
      }
      // 初始化头像文件列表
      if (this.dataForm.picture) {
        this.avatarFileList = [{
          url: this.dataForm.picture
        }];
      }
      this.getOptions();
    },
    getOptions() {
      baseStore
        .getDictionaryData({
          sort: "sex",
        })
        .then((res) => {
          this.genderOptions = JSON.parse(JSON.stringify(res));
        });
      baseStore
        .getDictionaryData({
          sort: "Nation",
        })
        .then((res) => {
          this.nationOptions = JSON.parse(JSON.stringify(res));
        });

      baseStore
        .getDictionaryData({
          sort: "politicalGroupOptions",
        })
        .then((res) => {
          this.politicalOptions = JSON.parse(JSON.stringify(res));
        });

      baseStore
        .getDictionaryData({
          sort: "professionalTitle",
        })
        .then((res) => {
          this.titleOptions = JSON.parse(JSON.stringify(res));
        });

      baseStore
        .getDictionaryData({
          sort: "positionOptions",
        })
        .then((res) => {
          this.positionOptions = JSON.parse(JSON.stringify(res));
        });

      baseStore
        .getDictionaryData({
          sort: "medicallevelOptions",
        })
        .then((res) => {
          this.medicalLevelOptions = JSON.parse(JSON.stringify(res));
        });

      baseStore
        .getDictionaryData({
          sort: "vocationalstatusOptions",
        })
        .then((res) => {
          this.vocationalStatusOptions = JSON.parse(JSON.stringify(res));
        });

      baseStore
        .getDictionaryData({
          sort: "majorField",
        })
        .then((res) => {
          this.majorFieldOptions = JSON.parse(JSON.stringify(res));
        });
      baseStore
        .getDictionaryData({
          sort: "degree",
        })
        .then((res) => {
          this.degreeOptions = JSON.parse(JSON.stringify(res));
        });
      baseStore
        .getDictionaryData({
          sort: "educationLevel",
        })
        .then((res) => {
          this.educationLevelOptions = JSON.parse(JSON.stringify(res));
        });

      this.show = true;
    },

    // 处理出生日期变更
    onBirthdateChange(e) {
      this.dataForm.birthdate = e.detail.value;
    },

    // 清除出生日期
    clearBirthdate() {
      this.dataForm.birthdate = '';
    },

    // 处理毕业时间变更
    onGraduationTimeChange(e) {
      this.dataForm.graduationTime = e.detail.value;
    },

    // 清除毕业时间
    clearGraduationTime() {
      this.dataForm.graduationTime = '';
    },

    // 切换表单部分的展开/收起状态
    toggleSection(sectionName) {
      this.openSections[sectionName] = !this.openSections[sectionName];
    },

    // 选择头像
    selectAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ["original", "compressed"],
        success: (res) => {
          // #ifdef H5
          let isAccept = new RegExp("image/*").test(res.tempFiles[0].type);
          if (!isAccept) return this.$u.toast(`请上传图片`);
          // #endif
          let tempFilePaths = res.tempFilePaths[0];
          uni.uploadFile({
            url: this.baseURL + "/api/flowUp/basis/uploaderAttachment",
            filePath: tempFilePaths,
            name: "file",
            header: {
              Authorization: uni.getStorageSync('token'),
            }, formData: {
              folder: 'flowup/picture',
            },
            success: (uploadFileRes) => {
              let data = JSON.parse(uploadFileRes.data);
              if (data.code === 200) {
                this.dataForm.picture = data.data.url;
              } else {
                this.$u.toast(data.msg);
              }
            },
            fail: (err) => {
              this.$u.toast("头像更换失败");
            },
          });
        },
      });
    },

    // 头像上传确认回调
    onAvatarUploadConfirm(fileList) {
      if (fileList && fileList.length > 0) {
        this.dataForm.picture = fileList[0].url;
        this.avatarFileList = [...fileList];
      }
    },

    submit() {
      updatePhysician(this.dataForm).then((res) => {
        uni.showToast({
          title: "保存成功",
          duration: 800,
          icon: "none",
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1000);
      });
    },

    // 重置表单
    resetForm() {
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  min-height: 100vh;
}

.physician-form-container {
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 200rpx;
}

// 顶部医生信息卡片
.physician-info-card {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 12rpx 40rpx rgba(25, 118, 210, 0.3);
  color: #fff;

  .physician-header {
    display: flex;
    align-items: center;
    gap: 24rpx;

    .physician-avatar {
      width: 120rpx;
      height: 120rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 3rpx solid rgba(255, 255, 255, 0.3);
      position: relative;
      overflow: hidden;

      .avatar-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .avatar-text {
        color: #fff;
        font-size: 40rpx;
        font-weight: 700;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
      }

      .avatar-edit-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;

        .edit-icon {
          font-size: 40rpx;
          color: #fff;
        }
      }

      &:active,
      &:hover {
        .avatar-edit-overlay {
          opacity: 1;
        }
      }
    }

    .physician-details {
      flex: 1;
      min-width: 0;

      .physician-name {
        font-size: 36rpx;
        font-weight: 700;
        color: #fff;
        margin-bottom: 12rpx;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
      }

      .physician-meta {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .meta-label {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
        }

        .meta-value {
          font-size: 26rpx;
          color: #fff;
          font-weight: 600;
        }
      }
    }
  }
}

// 表单区域
.form-sections {
  .form-section {
    background: #fff;
    border-radius: 20rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(25, 118, 210, 0.08);
    border: 1rpx solid rgba(25, 118, 210, 0.1);
    transition: all 0.3s ease;

    .section-header {
      background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 100%);
      padding: 24rpx 28rpx;
      display: flex;
      align-items: center;
      gap: 16rpx;
      border-bottom: 1rpx solid rgba(25, 118, 210, 0.1);
      cursor: pointer;
      user-select: none;

      .section-icon {
        font-size: 32rpx;
      }

      .section-title {
        font-size: 32rpx;
        font-weight: 700;
        color: #1565c0;
        flex: 1;
      }

      .section-toggle {
        .toggle-icon {
          font-size: 28rpx;
          color: #1565c0;
          transition: transform 0.3s ease;
        }
      }
    }

    .section-content {
      padding: 28rpx;
    }

    .form-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 24rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .form-label {
        width: 220rpx;
        font-size: 28rpx;
        color: #1565c0;
        font-weight: 600;
        margin-right: 20rpx;
        flex-shrink: 0;
        padding-top: 10rpx;

        &.required::after {
          content: "*";
          color: #f44336;
          margin-left: 4rpx;
        }
      }

      .form-field {
        flex: 1;
        min-width: 0;

        &.date-field {
          display: flex;
          align-items: center;
          gap: 16rpx;
        }
      }

      .form-input,
      .form-select,
      .form-datepicker,
      .form-textarea {

        ::v-deep .u-input,
        ::v-deep .xunda-select,
        ::v-deep .xunda-date-picker,
        ::v-deep .u-textarea {
          width: 100%;
          min-height: 60rpx;
          padding: 20rpx;
          border: 2rpx solid #e9ecef;
          border-radius: 16rpx;
          font-size: 28rpx;
          color: #333;
          background: #fff;
          transition: all 0.3s ease;
          box-sizing: border-box;

          &:focus {
            border-color: #1976d2;
            box-shadow: 0 0 0 6rpx rgba(25, 118, 210, 0.1);
            outline: none;
          }

          &::placeholder {
            color: #adb5bd;
            font-size: 26rpx;
          }
        }

        ::v-deep .u-textarea {
          min-height: 120rpx;
          resize: none;
        }
      }

      .date-picker {
        flex: 1;

        .date-picker-inner {
          width: 100%;
          min-height: 60rpx;
          padding: 20rpx;
          border: 2rpx solid #e9ecef;
          border-radius: 16rpx;
          font-size: 28rpx;
          color: #333;
          background: #fff;
          transition: all 0.3s ease;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .date-text {
            flex: 1;
          }

          .arrow-icon {
            color: #999;
            font-size: 24rpx;
          }
        }
      }

      .clear-input-btn,
      .clear-date-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border-radius: 16rpx;
        border: 2rpx solid #e9ecef;

        .clear-icon {
          color: #999;
          font-size: 28rpx;
        }
      }
    }
  }
}

// 底部操作按钮
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 20rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -8rpx 32rpx rgba(25, 118, 210, 0.15);
  border-top: 1rpx solid rgba(25, 118, 210, 0.1);

  .action-row {
    display: flex;
    gap: 16rpx;

    .action-btn {
      flex: 1;
      height: 88rpx;
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12rpx;
      font-weight: 600;
      transition: all 0.3s ease;
      border: 2rpx solid transparent;

      .btn-icon {
        font-size: 28rpx;
      }

      .btn-text {
        font-size: 28rpx;
      }

      &.primary-btn {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        color: #fff;
        box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.3);

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.3);
        }
      }

      &.secondary-btn {
        background: #f8f9fa;
        color: #6c757d;
        border-color: #dee2e6;

        &:active {
          background: #e9ecef;
        }
      }

      &.button-hover {
        transform: scale(0.98);
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .physician-form-container {
    padding: 16rpx;
    padding-bottom: 180rpx;
  }

  .physician-info-card {
    padding: 24rpx;

    .physician-header {
      gap: 16rpx;

      .physician-avatar {
        width: 100rpx;
        height: 100rpx;

        .avatar-text {
          font-size: 32rpx;
        }
      }

      .physician-details {
        .physician-name {
          font-size: 32rpx;
        }

        .physician-meta {
          .meta-label {
            font-size: 22rpx;
          }

          .meta-value {
            font-size: 24rpx;
          }
        }
      }
    }
  }

  .form-sections {
    .form-section {
      .section-header {
        padding: 20rpx 24rpx;

        .section-icon {
          font-size: 28rpx;
        }

        .section-title {
          font-size: 28rpx;
        }
      }

      .section-content {
        padding: 20rpx;

        .form-item {
          margin-bottom: 16rpx;

          .form-label {
            font-size: 26rpx;
            width: 180rpx;
          }

          ::v-deep .u-input,
          ::v-deep .xunda-select,
          ::v-deep .xunda-date-picker,
          ::v-deep .u-textarea {
            padding: 16rpx;
            font-size: 26rpx;

            &::placeholder {
              font-size: 24rpx;
            }
          }

          .date-picker {
            .date-picker-inner {
              padding: 16rpx;
              font-size: 26rpx;
            }
          }

          .clear-input-btn,
          .clear-date-btn {
            width: 56rpx;
            height: 56rpx;
          }
        }
      }
    }
  }

  .action-buttons {
    padding: 20rpx 16rpx;

    .action-row {
      gap: 12rpx;

      .action-btn {
        height: 80rpx;

        .btn-icon {
          font-size: 24rpx;
        }

        .btn-text {
          font-size: 26rpx;
        }
      }
    }
  }
}
</style>