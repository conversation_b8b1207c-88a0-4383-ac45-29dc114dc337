<template>
    <view class="list-view">
        <view class="search-box_sticky">
            <view class="search-box">
                <!-- 使用BitSearch组件替换原来的u-search -->
                <BitSearch @search="handleSearch" />
            </view>
            <view class="date-picker-container">
                <!-- 添加清空日期功能并美化界面 -->
                <view class="date-picker-wrapper">
                    <picker class="date-picker" mode="date" fields="month" :value="selectedDate" @change="onDateChange">
                        <view class="date-picker-inner">
                            <text class="date-text">{{ selectedDate ? formatDisplayDate(selectedDate) : '请选择月份'
                            }}</text>
                            <text class="arrow-icon">▼</text>
                        </view>
                    </picker>
                    <view v-if="selectedDate" class="clear-date-btn" @click="clearDate">
                        <text class="clear-icon">✕</text>
                    </view>
                </view>
            </view>
        </view>

        <view class="visit-list-section">
            <mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption"
                :fixed="false">
                <view class="date-group" v-for="(group, date) in groupedList" :key="date">
                    <view class="date-header">
                        <text class="date-text">{{ formatDate(date) }}</text>
                        <text class="count-badge">{{ group.length }}条记录</text>
                    </view>

                    <view class="visit-card" v-for="(item, index) in group" :key="index" @click="goDetail(item)">
                        <view class="visit-header">
                            <view class="patient-info">
                                <text class="name">{{ item.patientName }}</text>
                                <text class="id">{{ item.patientAdmissionNo }}</text>
                            </view>
                            <view class="doctor-info">
                                <text class="label">随访医师:</text>
                                <text class="name">{{ item.physicianName }}</text>
                            </view>
                        </view>

                        <view class="visit-time">
                            <u-icon name="clock" size="28" color="#94a3b8"></u-icon>
                            <text>{{ formatTime(item.visiteDate) }}</text>
                        </view>
                    </view>
                </view>

                <view class="empty-state" v-if="list.length === 0">
                    <u-empty mode="data" icon="http://cdn.uviewui.com/uview/empty/data.png"></u-empty>
                </view>
            </mescroll-uni>
        </view>
    </view>
</template>
<script>
import resources from "@/libs/resources.js";
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import { getAppList } from "@/api/flow-up/visitRecord";
import BitSearch from "@/components/gzbit/BitSearch.vue";

export default {
    mixins: [MescrollMixin],
    components: {
        BitSearch,
    },
    data() {
        return {
            selectedDate: "", // 将selectedMonth替换为selectedDate
            scrollLeft: 0,
            monthList: [],
            groupedList: {},
            isAuthority: true,
            icon: resources.message.nodata,
            sortValue: [],
            searchForm: {
                name: undefined,
            },
            downOption: {
                use: true,
                auto: false,
            },
            dataOptions: {},
            upOption: {
                page: {
                    num: 0,
                    size: 20,
                    time: null,
                },
                empty: {
                    use: true,
                    icon: resources.message.nodata,
                    tip: "暂无数据",
                    fixed: true,
                    zIndex: 5,
                },
                textNoMore: "没有更多数据",
                toTop: {
                    bottom: 250,
                },
            },
            list: [],
            appColumnList: [
                {
                    prop: "patientAdmissionNo",
                    label: "住院号",
                },
                {
                    prop: "patientName",
                    label: "随访患者",
                    width: 100,
                    align: "center",
                    sort: true,
                },
                {
                    prop: "physicianName",
                    label: "随访医师",
                    width: 100,
                    align: "center",
                    sort: true,
                },
                {
                    prop: "visiteDate",
                    label: "随访日期",
                    width: 100,
                    align: "center",
                    sort: true,
                },
            ],
            listQuery: {
                moduleId: "661876504619124229",
                sidx: "",
                keyword: "",
                json: "",
            },
            options: [],
            sortOptions: [],
            ableAll: {},
            interfaceRes: {
                name: [],
            },
            menuId: "",
            columnList: [],
            key: new Date(),
            dataValue: {},
            userInfo: {},
            firstInitSearchData: false,
            tabList: [],
            tabKey: 0,
            optionsObj: {
                defaultProps: {
                    label: "fullName",
                    value: "enCode",
                    multiple: false,
                    children: "",
                },
            },
            type: "",
        };
    },
    computed: {},
    onLoad(e) {
        this.type = e.type;
        this.userInfo = uni.getStorageSync("userInfo") || {};
        this.menuId = e.menuId;
        this.setDefaultQuery();
        this.dataAll();
        this.getColumnList();
    },
    onShow() {
        this.$nextTick(() => {
            this.mescroll.resetUpScroll();
        });
    },
    onUnload() {
        uni.$off("refresh");
    },
    methods: {

        formatDate(dateStr) {
            // 如果是日期对象或日期字符串
            if (typeof dateStr === 'string') {
                const date = new Date(dateStr);
                if (!isNaN(date.getTime())) {
                    return `${date.getMonth() + 1}月${date.getDate()}日`;
                }
            } else if (dateStr instanceof Date) {
                return `${dateStr.getMonth() + 1}月${dateStr.getDate()}日`;
            }
            // 原有的日期格式化逻辑保持不变
            const date = new Date(dateStr);
            return `${date.getMonth() + 1}月${date.getDate()}日`;
        },

        formatTime(dateStr) {
            const date = new Date(dateStr);
            const hours = date.getHours().toString().padStart(2, "0");
            const minutes = date.getMinutes().toString().padStart(2, "0");
            return `${hours}:${minutes}`;
        },

        groupListByDate(list) {
            const grouped = {};
            list.forEach((item) => {
                const date = item.visiteDate.split(" ")[0]; // 获取日期部分
                if (!grouped[date]) {
                    grouped[date] = [];
                }
                grouped[date].push(item);
            });

            // 按日期倒序排序
            this.groupedList = Object.keys(grouped)
                .sort((a, b) => new Date(b) - new Date(a))
                .reduce((acc, key) => {
                    acc[key] = grouped[key];
                    return acc;
                }, {});
        },

        toThousands(val, column) {
            if (val) {
                let valList = val.toString().split(".");
                let num = Number(valList[0]);
                let newVal = column.thousands ? num.toLocaleString() : num;
                return valList[1] ? newVal + "." + valList[1] : newVal;
            } else {
                return val;
            }
        },
        dataAll() {
            // getPhysicianAllDictionaryData(this.optionsObj);
        },
        openData(e) { },
        setDefaultQuery() {
            const defaultSortConfig = [];
            const sortField = defaultSortConfig.map(
                (o) => (o.sort === "desc" ? "-" : "") + o.field
            );
            this.listQuery.sidx = sortField.join(",");
        },
        //初始化查询的默认数据
        async initSearchData() {
            this.dataValue = JSON.parse(JSON.stringify(this.searchForm));
        },
        relationFormClick(item, column) {
            let vModel = column.__vModel__ + "_id";
            let id = item[vModel];
            let modelId = column.modelId;
            if (!id || !modelId) return;
            let config = {
                modelId: modelId,
                id: id,
                formTitle: "详情",
                noShowBtn: 1,
            };
            this.$nextTick(() => {
                const url =
                    "/pages/apply/dynamicModel/detail?config=" +
                    this.xunda.base64.encode(JSON.stringify(config), "UTF-8");
                uni.navigateTo({
                    url: url,
                });
            });
        },
        async upCallback(page) {
            if (!this.firstInitSearchData) {
                await this.initSearchData();
                this.firstInitSearchData = true;
            }

            const query = {
                currentPage: page.num,
                pageSize: page.size,
                menuId: this.menuId,
                ...this.listQuery,
                ...this.searchForm,
                dataType: 0,
                patientFlag: this.type === "patient",
                physicianFlag: this.type === "physician",
            };

            // 更新日期筛选逻辑，使用selectedDate而不是selectedMonth
            if (this.selectedDate) {
                const selectedDate = new Date(this.selectedDate);
                const year = selectedDate.getFullYear();
                const month = selectedDate.getMonth() + 1; // 月份从0开始，需要+1

                // 设置月初和月末日期
                query.startDate = `${year}-${month.toString().padStart(2, "0")}-01`;

                // 计算月末日期
                const nextMonth = month === 12 ? 1 : month + 1;
                const nextYear = month === 12 ? year + 1 : year;
                const lastDay = new Date(nextYear, nextMonth - 1, 0).getDate();

                query.endDate = `${year}-${month.toString().padStart(2, "0")}-${lastDay.toString().padStart(2, "0")}`;
            }
            getAppList(query)
                .then((res) => {
                    let _list = res.data.list;
                    this.mescroll.endSuccess(_list.length);
                    if (page.num == 1) this.list = [];
                    this.list = this.list.concat(_list);
                    // 按日期分组数据
                    this.groupListByDate(this.list);
                })
                .catch(() => {
                    this.mescroll.endSuccess(this.list.length);
                });
        },
        open(index) {
            this.list[index].show = true;
            this.list.map((val, idx) => {
                if (index != idx) this.list[idx].show = false;
            });
        },
        search() {
            if (this.isPreview == "1") return;
            this.searchTimer && clearTimeout(this.searchTimer);
            this.searchTimer = setTimeout(() => {
                this.list = [];
                this.mescroll.resetUpScroll();
            }, 300);
        },

        // 添加handleSearch方法处理BitSearch组件的搜索事件
        handleSearch(keyword) {
            this.listQuery.keyword = keyword;
            this.search();
        },

        goDetail(item) {
            let id = item.id;
            uni.navigateTo({
                url:
                    "/pages/flow-up/doVisitRecord?actionType=" + "detail" + "&id=" + id,
            });
        },

        // 添加日期格式化方法（用于显示）
        formatDisplayDate(dateStr) {
            if (!dateStr) return '';
            const date = new Date(dateStr);
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            return `${year}年${month}月`;
        },

        // 添加日期选择变更处理方法
        onDateChange(e) {
            console.log('日期选择变更：', e);
            this.selectedDate = e.detail.value;
            this.search();
        },

        // 添加清空日期方法
        clearDate() {
            this.selectedDate = "";
            this.search();
        },

        getColumnList() {
            let columnPermissionList = [];
            let _appColumnList = this.appColumnList;
            for (let i = 0; i < _appColumnList.length; i++) {
                columnPermissionList.push(_appColumnList[i]);
            }
            this.columnList = columnPermissionList;
        },
        cellClick(item) {
            const findIndex = this.sortValue.findIndex((o) => o === item.value);
            if (findIndex < 0) {
                const findLikeIndex = this.sortValue.findIndex(
                    (o) => o.indexOf(item.sidx) > -1
                );
                if (findLikeIndex > -1) this.sortValue.splice(findLikeIndex, 1);
                this.sortValue.push(item.value);
            } else {
                this.sortValue.splice(findIndex, 1);
            }
        },
        handleSortReset() {
            this.sortValue = [];
        },
        handleSortSearch() {
            if (this.sortValue.length) {
                this.listQuery.sidx = this.sortValue.join(",");
            } else {
                this.setDefaultQuery();
            }
            this.$refs.uDropdown.close();
            this.$nextTick(() => {
                this.list = [];
                this.mescroll.resetUpScroll();
            });
        },
        reset() {
            this.searchForm = JSON.parse(JSON.stringify(this.dataValue));
            this.key = new Date();
        },
        closeDropdown() {
            this.$refs.uDropdown.close();
            this.$nextTick(() => {
                this.list = [];
                this.mescroll.resetUpScroll();
            });
        },

        dataList(data) {
            let _list = data.list;
            return _list;
        },
    },
};
</script>

<style lang="scss">
page {
    background-color: #f5f7fa;
    height: 100%;
}

.list-view {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.search-box_sticky {
    background-color: #fff;
    padding: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 美化日期选择器样式 */
.date-picker-container {
    margin-top: 20rpx;

    .date-picker-wrapper {
        display: flex;
        align-items: center;
        background: #f8fafc;
        border-radius: 32rpx;
        padding: 12rpx 20rpx;

        .date-picker {
            flex: 1;

            .date-picker-inner {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 26rpx;
                color: #64748b;

                .date-text {
                    flex: 1;
                }

                .arrow-icon {
                    font-size: 20rpx;
                    color: #94a3b8;
                }
            }
        }

        .clear-date-btn {
            width: 40rpx;
            height: 40rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #e2e8f0;
            border-radius: 50%;
            margin-left: 16rpx;

            .clear-icon {
                color: #94a3b8;
                font-size: 24rpx;
            }
        }
    }
}

.visit-list-section {
    flex: 1;
    overflow: hidden;
}

.date-group {
    margin: 0 20rpx 20rpx;
}

.date-header {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    padding: 0 10rpx;
}

.date-text {
    font-size: 28rpx;
    color: #64748b;
    font-weight: 500;
}

.count-badge {
    margin-left: 16rpx;
    font-size: 24rpx;
    color: #94a3b8;
    background: #f1f5f9;
    padding: 4rpx 12rpx;
    border-radius: 20rpx;
}

.visit-card {
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.visit-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16rpx;
}

.patient-info {
    .name {
        font-size: 32rpx;
        color: #1e293b;
        font-weight: 500;
    }

    .id {
        margin-left: 16rpx;
        font-size: 26rpx;
        color: #64748b;
    }
}

.doctor-info {
    .label {
        font-size: 26rpx;
        color: #94a3b8;
    }

    .name {
        margin-left: 8rpx;
        font-size: 26rpx;
        color: #1e293b;
    }
}

.visit-time {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #64748b;

    text {
        margin-left: 8rpx;
    }
}

.floating-button {
    position: fixed;
    right: 32rpx;
    bottom: 32rpx;
    width: 96rpx;
    height: 96rpx;
    background: linear-gradient(135deg, #4a86e8, #60a5fa);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(74, 134, 232, 0.3);
    transition: transform 0.3s ease;

    &:active {
        transform: scale(0.95);
    }
}

.empty-state {
    padding: 60rpx;
    text-align: center;
}
</style>
