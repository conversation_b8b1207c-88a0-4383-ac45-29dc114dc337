@import "@/assets/styles/flowup-design.scss";

<template>
    <view class="visit-record-container">
        <!-- 美化的顶部搜索和筛选区域 -->
        <view class="header-section">
            <view class="header-content">
                <!-- 搜索框 -->
                <view class="search-section">
                    <BitSearch @search="handleSearch" />
                </view>

                <!-- 日期筛选 -->
                <view class="filter-section">
                    <view class="date-filter-wrapper">
                        <picker class="date-picker" mode="date" fields="month" :value="selectedDate"
                            @change="onDateChange">
                            <view class="date-picker-inner flowup-card">
                                <text class="date-icon">📅</text>
                                <text class="date-text">{{ selectedDate ? formatDisplayDate(selectedDate) : '选择月份'
                                    }}</text>
                                <text class="arrow-icon" :class="{ 'arrow-up': false }">▼</text>
                            </view>
                        </picker>
                        <view v-if="selectedDate" class="clear-date-btn flowup-action-btn secondary-btn"
                            @click="clearDate">
                            <text class="clear-icon">✕</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 随访记录列表 -->
        <view class="visit-list-section">
            <mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption"
                :fixed="false">
                <view class="visit-list-content">
                    <!-- 日期分组 -->
                    <uni-transition v-for="(group, date) in groupedList" :key="date" mode-class="fade" :duration="300"
                        :show="true">
                        <view class="date-group">
                            <view class="date-header flowup-section-header">
                                <view class="date-info">
                                    <text class="date-icon">📅</text>
                                    <text class="date-text">{{ formatDate(date) }}</text>
                                </view>
                                <view class="count-badge flowup-status-badge status-in-progress">{{ group.length }}条
                                </view>
                            </view>

                            <!-- 随访记录卡片 -->
                            <uni-transition v-for="(item, index) in group" :key="index" mode-class="slide-left"
                                :duration="300" :delay="index * 50" :show="true">
                                <view class="visit-card flowup-card" @click="goDetail(item)">
                                    <view class="visit-header">
                                        <view class="patient-section">
                                            <view class="patient-avatar flowup-avatar">
                                                <text class="avatar-text">{{ getPatientInitial(item.patientName)
                                                }}</text>
                                            </view>
                                            <view class="patient-info">
                                                <view class="patient-name">{{ item.patientName }}</view>
                                                <view class="patient-id">
                                                    <text class="id-icon">🏥</text>
                                                    <text class="id-text">{{ item.patientAdmissionNo }}</text>
                                                </view>
                                            </view>
                                        </view>

                                        <view class="visit-status flowup-status-badge status-completed">
                                            <text class="status-text">已完成</text>
                                        </view>
                                    </view>

                                    <view class="visit-details">
                                        <view class="detail-item">
                                            <text class="detail-icon">👨‍⚕️</text>
                                            <text class="detail-label">随访医师:</text>
                                            <text class="detail-value">{{ item.physicianName }}</text>
                                        </view>

                                        <view class="detail-item">
                                            <text class="detail-icon">🕒</text>
                                            <text class="detail-label">随访时间:</text>
                                            <text class="detail-value">{{ formatTime(item.visiteDate) }}</text>
                                        </view>
                                    </view>
                                </view>
                            </uni-transition>
                        </view>
                    </uni-transition>

                    <!-- 空状态 -->
                    <EmptyState v-if="list.length === 0 && !loading" icon="📋" text="暂无随访记录" desc="完成随访后将在此显示" />
                </view>
            </mescroll-uni>
        </view>
    </view>
</template>
<script>
import resources from "@/libs/resources.js";
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import { getAppList } from "@/api/flow-up/visitRecord";
import BitSearch from "@/components/gzbit/BitSearch.vue";
import EmptyState from "@/components/gzbit/EmptyState.vue";

export default {
    mixins: [MescrollMixin],
    components: {
        BitSearch,
        EmptyState,
    },
    data() {
        return {
            selectedDate: "", // 将selectedMonth替换为selectedDate
            scrollLeft: 0,
            monthList: [],
            groupedList: {},
            isAuthority: true,
            icon: resources.message.nodata,
            sortValue: [],
            searchForm: {
                name: undefined,
            },
            downOption: {
                use: true,
                auto: false,
            },
            dataOptions: {},
            upOption: {
                page: {
                    num: 0,
                    size: 20,
                    time: null,
                },
                empty: {
                    use: true,
                    icon: resources.message.nodata,
                    tip: "暂无数据",
                    fixed: true,
                    zIndex: 5,
                },
                textNoMore: "没有更多数据",
                toTop: {
                    bottom: 250,
                },
            },
            list: [],
            appColumnList: [
                {
                    prop: "patientAdmissionNo",
                    label: "住院号",
                },
                {
                    prop: "patientName",
                    label: "随访患者",
                    width: 100,
                    align: "center",
                    sort: true,
                },
                {
                    prop: "physicianName",
                    label: "随访医师",
                    width: 100,
                    align: "center",
                    sort: true,
                },
                {
                    prop: "visiteDate",
                    label: "随访日期",
                    width: 100,
                    align: "center",
                    sort: true,
                },
            ],
            listQuery: {
                moduleId: "661876504619124229",
                sidx: "",
                keyword: "",
                json: "",
            },
            options: [],
            sortOptions: [],
            ableAll: {},
            interfaceRes: {
                name: [],
            },
            menuId: "",
            columnList: [],
            key: new Date(),
            dataValue: {},
            userInfo: {},
            firstInitSearchData: false,
            tabList: [],
            tabKey: 0,
            optionsObj: {
                defaultProps: {
                    label: "fullName",
                    value: "enCode",
                    multiple: false,
                    children: "",
                },
            },
            type: "",
            loading: false,
        };
    },
    computed: {
        // 总记录数
        totalRecords() {
            return this.list.length;
        },
        // 总天数
        totalDays() {
            return Object.keys(this.groupedList).length;
        },
        // 唯一患者数
        uniquePatients() {
            const patientNames = new Set(this.list.map(item => item.patientName));
            return patientNames.size;
        }
    },
    onLoad(e) {
        this.type = e.type;
        this.userInfo = uni.getStorageSync("userInfo") || {};
        this.menuId = e.menuId;
        this.setDefaultQuery();
        this.dataAll();
        this.getColumnList();
    },
    onShow() {
        this.$nextTick(() => {
            this.mescroll.resetUpScroll();
        });
    },
    onUnload() {
        uni.$off("refresh");
    },
    methods: {
        // 获取患者姓名首字母
        getPatientInitial(name) {
            if (!name) return '?';
            return name.charAt(0).toUpperCase();
        },

        // 刷新列表
        refreshList() {
            this.list = [];
            this.groupedList = {};
            this.mescroll.resetUpScroll();
        },

        // 滚动到顶部
        scrollToTop() {
            this.mescroll.scrollTo(0, 300);
        },

        formatDate(dateStr) {
            // 如果是日期对象或日期字符串
            if (typeof dateStr === 'string') {
                const date = new Date(dateStr);
                if (!isNaN(date.getTime())) {
                    return `${date.getMonth() + 1}月${date.getDate()}日`;
                }
            } else if (dateStr instanceof Date) {
                return `${dateStr.getMonth() + 1}月${dateStr.getDate()}日`;
            }
            // 原有的日期格式化逻辑保持不变
            const date = new Date(dateStr);
            return `${date.getMonth() + 1}月${date.getDate()}日`;
        },

        formatTime(dateStr) {
            const date = new Date(dateStr);
            const hours = date.getHours().toString().padStart(2, "0");
            const minutes = date.getMinutes().toString().padStart(2, "0");
            return `${hours}:${minutes}`;
        },

        groupListByDate(list) {
            const grouped = {};
            list.forEach((item) => {
                const date = item.visiteDate.split(" ")[0]; // 获取日期部分
                if (!grouped[date]) {
                    grouped[date] = [];
                }
                grouped[date].push(item);
            });

            // 按日期倒序排序
            this.groupedList = Object.keys(grouped)
                .sort((a, b) => new Date(b) - new Date(a))
                .reduce((acc, key) => {
                    acc[key] = grouped[key];
                    return acc;
                }, {});
        },

        toThousands(val, column) {
            if (val) {
                let valList = val.toString().split(".");
                let num = Number(valList[0]);
                let newVal = column.thousands ? num.toLocaleString() : num;
                return valList[1] ? newVal + "." + valList[1] : newVal;
            } else {
                return val;
            }
        },
        dataAll() {
            // getPhysicianAllDictionaryData(this.optionsObj);
        },
        openData() { },
        setDefaultQuery() {
            const defaultSortConfig = [];
            const sortField = defaultSortConfig.map(
                (o) => (o.sort === "desc" ? "-" : "") + o.field
            );
            this.listQuery.sidx = sortField.join(",");
        },
        //初始化查询的默认数据
        async initSearchData() {
            this.dataValue = JSON.parse(JSON.stringify(this.searchForm));
        },
        relationFormClick(item, column) {
            let vModel = column.__vModel__ + "_id";
            let id = item[vModel];
            let modelId = column.modelId;
            if (!id || !modelId) return;
            let config = {
                modelId: modelId,
                id: id,
                formTitle: "详情",
                noShowBtn: 1,
            };
            this.$nextTick(() => {
                const url =
                    "/pages/apply/dynamicModel/detail?config=" +
                    this.xunda.base64.encode(JSON.stringify(config), "UTF-8");
                uni.navigateTo({
                    url: url,
                });
            });
        },
        async upCallback(page) {
            if (!this.firstInitSearchData) {
                await this.initSearchData();
                this.firstInitSearchData = true;
            }

            const query = {
                currentPage: page.num,
                pageSize: page.size,
                menuId: this.menuId,
                ...this.listQuery,
                ...this.searchForm,
                dataType: 0,
                patientFlag: this.type === "patient",
                physicianFlag: this.type === "physician",
            };

            // 更新日期筛选逻辑，使用selectedDate而不是selectedMonth
            if (this.selectedDate) {
                const selectedDate = new Date(this.selectedDate);
                const year = selectedDate.getFullYear();
                const month = selectedDate.getMonth() + 1; // 月份从0开始，需要+1

                // 设置月初和月末日期
                query.startDate = `${year}-${month.toString().padStart(2, "0")}-01`;

                // 计算月末日期
                const nextMonth = month === 12 ? 1 : month + 1;
                const nextYear = month === 12 ? year + 1 : year;
                const lastDay = new Date(nextYear, nextMonth - 1, 0).getDate();

                query.endDate = `${year}-${month.toString().padStart(2, "0")}-${lastDay.toString().padStart(2, "0")}`;
            }
            getAppList(query)
                .then((res) => {
                    let _list = res.data.list;
                    this.mescroll.endSuccess(_list.length);
                    if (page.num == 1) this.list = [];
                    this.list = this.list.concat(_list);
                    // 按日期分组数据
                    this.groupListByDate(this.list);
                })
                .catch(() => {
                    this.mescroll.endSuccess(this.list.length);
                });
        },
        open(index) {
            this.list[index].show = true;
            this.list.map((_, idx) => {
                if (index != idx) this.list[idx].show = false;
            });
        },
        search() {
            if (this.isPreview == "1") return;
            this.searchTimer && clearTimeout(this.searchTimer);
            this.searchTimer = setTimeout(() => {
                this.list = [];
                this.mescroll.resetUpScroll();
            }, 300);
        },

        // 添加handleSearch方法处理BitSearch组件的搜索事件
        handleSearch(keyword) {
            this.listQuery.keyword = keyword;
            this.search();
        },

        goDetail(item) {
            let id = item.id;
            uni.navigateTo({
                url:
                    "/pages/flow-up/doVisitRecord?actionType=" + "detail" + "&id=" + id,
            });
        },

        // 添加日期格式化方法（用于显示）
        formatDisplayDate(dateStr) {
            if (!dateStr) return '';
            const date = new Date(dateStr);
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            return `${year}年${month}月`;
        },

        // 添加日期选择变更处理方法
        onDateChange(e) {
            console.log('日期选择变更：', e);
            this.selectedDate = e.detail.value;
            this.search();
        },

        // 添加清空日期方法
        clearDate() {
            this.selectedDate = "";
            this.search();
        },

        getColumnList() {
            let columnPermissionList = [];
            let _appColumnList = this.appColumnList;
            for (let i = 0; i < _appColumnList.length; i++) {
                columnPermissionList.push(_appColumnList[i]);
            }
            this.columnList = columnPermissionList;
        },
        cellClick(item) {
            const findIndex = this.sortValue.findIndex((o) => o === item.value);
            if (findIndex < 0) {
                const findLikeIndex = this.sortValue.findIndex(
                    (o) => o.indexOf(item.sidx) > -1
                );
                if (findLikeIndex > -1) this.sortValue.splice(findLikeIndex, 1);
                this.sortValue.push(item.value);
            } else {
                this.sortValue.splice(findIndex, 1);
            }
        },
        handleSortReset() {
            this.sortValue = [];
        },
        handleSortSearch() {
            if (this.sortValue.length) {
                this.listQuery.sidx = this.sortValue.join(",");
            } else {
                this.setDefaultQuery();
            }
            this.$refs.uDropdown.close();
            this.$nextTick(() => {
                this.list = [];
                this.mescroll.resetUpScroll();
            });
        },
        reset() {
            this.searchForm = JSON.parse(JSON.stringify(this.dataValue));
            this.key = new Date();
        },
        closeDropdown() {
            this.$refs.uDropdown.close();
            this.$nextTick(() => {
                this.list = [];
                this.mescroll.resetUpScroll();
            });
        },

        dataList(data) {
            let _list = data.list;
            return _list;
        },
    },
};
</script>

<style lang="scss">
@import "@/assets/styles/flowup-design.scss";

page {
    background-color: #f0f2f6;
    height: 100%;
    /* #ifdef MP-ALIPAY */
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    /* #endif */
}

.visit-record-container {
    min-height: 100%;
    background-color: #f0f2f6;
    padding-bottom: 20rpx;
}

// 美化的头部区域
.header-section {
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9999;
    background: linear-gradient(135deg, #d8dfe6 0%, #dae3ee 100%);
    padding: 24rpx 20rpx;
    box-shadow: 0 8rpx 32rpx rgba(208, 222, 236, 0.3);

    .header-content {
        display: flex;
        flex-direction: column;
        gap: 20rpx;

        // 筛选区域
        .filter-section {
            display: flex;
            flex-direction: column;
            gap: 16rpx;

            .date-filter-wrapper {
                position: relative;
                display: flex;
                align-items: center;
                gap: 16rpx;

                .date-picker {
                    flex: 1;

                    .date-picker-inner {
                        display: flex;
                        align-items: center;
                        gap: 12rpx;
                        padding: 16rpx 24rpx;
                        background: rgba(255, 255, 255, 0.95);
                        border-radius: 40rpx;
                        backdrop-filter: blur(10rpx);
                        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
                        border: 2rpx solid rgba(255, 255, 255, 0.2);
                        transition: all 0.3s ease;

                        &:active {
                            transform: scale(0.98);
                            background: rgba(255, 255, 255, 1);
                        }

                        .date-icon {
                            font-size: 24rpx;
                        }

                        .date-text {
                            flex: 1;
                            font-size: 26rpx;
                            color: #333;
                            font-weight: 500;
                        }

                        .arrow-icon {
                            font-size: 20rpx;
                            color: #666;
                            transition: transform 0.3s ease;

                            &.arrow-up {
                                transform: rotate(180deg);
                            }
                        }
                    }
                }

                .clear-date-btn {
                    width: 56rpx;
                    height: 56rpx;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 4rpx 16rpx rgb(255, 255, 255);
                    backdrop-filter: blur(10rpx);
                    transition: all 0.3s ease;

                    .clear-icon {
                        font-size: 20rpx;
                        font-weight: 700;
                    }

                    &:active {
                        transform: scale(0.9);
                    }
                }
            }
        }
    }
}

// 列表区域
.visit-list-section {
    flex: 1;
    overflow: hidden;
    padding: 32rpx 20rpx 120rpx;

    .visit-list-content {
        // 添加背景和圆角以增强内容区域的视觉效果
        background: rgba(248, 251, 255, 0.5);
        border-radius: 24rpx;
        padding: 20rpx;
        box-shadow: inset 0 0 10rpx rgba(25, 118, 210, 0.1);

        // 日期分组
        .date-group {
            margin-bottom: 32rpx;

            .date-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 20rpx;
                border-radius: 16rpx;

                .date-info {
                    display: flex;
                    align-items: center;
                    gap: 12rpx;

                    .date-icon {
                        font-size: 24rpx;
                    }

                    .date-text {
                        font-size: 28rpx;
                        font-weight: 600;
                    }
                }

                .count-badge {
                    padding: 8rpx 16rpx;
                    border-radius: 20rpx;
                    font-size: 22rpx;
                    font-weight: 600;
                }
            }
        }
    }
}

// 随访卡片样式
.visit-card {
    margin-bottom: 20rpx;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: 1rpx solid rgba(25, 118, 210, 0.15);
    box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.1);

    // 添加顶部装饰条
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6rpx;
        background: linear-gradient(90deg, #1976d2 0%, #1565c0 100%);
    }

    &:active {
        transform: translateY(-4rpx) scale(0.98);
        box-shadow: 0 12rpx 32rpx rgba(25, 118, 210, 0.15);
    }

    .visit-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 24rpx;
        padding: 24rpx 24rpx 0;

        .patient-section {
            display: flex;
            align-items: flex-start;
            gap: 20rpx;
            flex: 1;

            .patient-avatar {
                width: 80rpx;
                height: 80rpx;
                border-radius: 20rpx;
                background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.3);

                .avatar-text {
                    font-size: 32rpx;
                    font-weight: 700;
                    color: white;
                }
            }

            .patient-info {
                flex: 1;

                .patient-name {
                    font-size: 32rpx;
                    font-weight: 600;
                    margin-bottom: 8rpx;
                    line-height: 1.3;
                    color: #333;
                }

                .patient-id {
                    display: flex;
                    align-items: center;
                    gap: 8rpx;

                    .id-icon {
                        font-size: 20rpx;
                        color: #1976d2;
                    }

                    .id-text {
                        font-size: 24rpx;
                        font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
                        color: #666;
                    }
                }
            }
        }

        .visit-status {
            padding: 12rpx 20rpx;
            border-radius: 40rpx;
            font-size: 22rpx;
            font-weight: 600;
        }
    }

    .visit-details {
        margin-bottom: 24rpx;
        padding: 0 24rpx;

        .detail-item {
            display: flex;
            align-items: center;
            gap: 12rpx;
            margin-bottom: 16rpx;
            padding: 16rpx;
            background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 100%);
            border-radius: 16rpx;
            border: 1rpx solid rgba(25, 118, 210, 0.1);

            .detail-icon {
                font-size: 24rpx;
                width: 32rpx;
                text-align: center;
                color: #1976d2;
            }

            .detail-label {
                font-size: 26rpx;
                color: #1565c0;
                min-width: 120rpx;
                font-weight: 500;
            }

            .detail-value {
                font-size: 26rpx;
                flex: 1;
                color: #333;
                font-weight: 400;
            }
        }
    }

    .card-actions {
        display: flex;
        justify-content: flex-end;
        padding: 20rpx 24rpx;
        border-top: 1rpx solid rgba(25, 118, 210, 0.1);
        background: linear-gradient(to bottom, rgba(248, 251, 255, 0.6), rgba(227, 242, 253, 0.8));

        .flowup-action-btn {
            padding: 16rpx 32rpx;
            border-radius: 40rpx;
            font-size: 26rpx;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8rpx;
            transition: all 0.3s ease;

            .btn-icon {
                font-size: 24rpx;
            }

            &:active {
                transform: scale(0.95);
            }
        }
    }
}

// 空状态样式
// 页面不再定义空状态样式，统一复用组件样式

// 浮动操作按钮
.fab-container {
    position: fixed;
    bottom: 120rpx;
    right: 40rpx;
    z-index: 999;

    .flowup-action-btn {
        width: 96rpx;
        height: 96rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8rpx 32rpx rgba(25, 118, 210, 0.4);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        .fab-icon {
            font-size: 32rpx;
            filter: grayscale(1) brightness(10);
        }

        &:active {
            transform: scale(0.9);
            box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.4);
        }
    }
}
</style>
