<template>
  <view class="physician-container">
    <!-- 功能网格 -->
    <view class="grid-section">
      <view class="section-title">医疗服务</view>
      <view class="grid-container">
        <view class="grid-item" v-for="item in gridItems" :key="item.id" @click="navigateTo(item.pagePath)">
          <view class="grid-icon-wrapper">
            <image :src="item.iconPath" class="grid-icon"></image>
          </view>
          <view class="grid-text">{{ item.text }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
var wv; //计划创建的webview
import logoImg from "@/static/logo.png";
import { getMyPhysicianAsync, scanBindAsync } from "@/api/flow-up/physician";
// #ifndef MP
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import IndexMixin from "./mixin.js";
// #endif

export default {
  // #ifndef MP
  mixins: [MescrollMixin, IndexMixin],
  // #endif
  components: {},
  data() {
    return {
      logoImg,
      gridItems: [],
      myPhysician: null,
      avatar: "",
    };
  },
  methods: {
    navigateTo(pagePath) {
      uni.navigateTo({
        url: pagePath,
      });
    },
    navigateToDoctor() {
      const params = {
        // 在这里添加你需要的参数
      };
      // 将参数转换为查询字符串
      const queryString = Object.keys(params)
        .map(
          (key) =>
            `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`
        )
        .join("&");
      const pagePath = "/pages/flow-up/doctor/index";
      // 如果有参数，添加到URL后面
      const url = queryString ? `${pagePath}?${queryString}` : pagePath;
      // 跳转到医师界面
      uni.navigateTo({
        url: url,
      });
    },
    loadMyInfo() {
      var data = this;
      getMyPhysicianAsync().then((res) => {
        data.myPhysician = res.data;
        if (res.data) {
          this.avatar = this.define.baseURL + res.data.picture;
        }
      });
    },
    // 扫码绑定医生
    scanBind() {
      scanBindAsync().then(res => {
        if (res.code === 200) {
          uni.showToast({
            title: '绑定成功',
            icon: 'success'
          });
          this.loadMyInfo();
        } else {
          uni.showToast({
            title: res.msg || '绑定失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        uni.showToast({
          title: '绑定失败',
          icon: 'none'
        });
      });
    }
  },
  computed: {},
  created() {
    this.gridItems = [
      {
        id: 1,
        text: "我的患者",
        iconPath: "/static/image/flow-up/my-patient.png",
        pagePath: "/pages/flow-up/physician/myPatient",
      },
      {
        id: 2,
        text: "我的随访",
        iconPath: "/static/image/flow-up/my-record.png",
        pagePath: "/pages/flow-up/myVisitRecord?type=physician",
      },
    ];
  },
  onLoad() {
    this.loadMyInfo();
  }
};
</script>

<style lang="scss">
.physician-container {
  min-height: 100vh;
  padding-bottom: 40rpx;
}


// 功能网格区域
.grid-section {
  padding: 0 30rpx;

  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #1565c0;
    margin-bottom: 30rpx;
    padding-left: 16rpx;
    border-left: 8rpx solid #1976d2;
  }

  .grid-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20rpx;

    .grid-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 30rpx 20rpx;
      background: #fff;
      border-radius: 24rpx;
      box-shadow: 0 8rpx 20rpx rgba(25, 118, 210, 0.15);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(25, 118, 210, 0.08) 0%, rgba(21, 101, 192, 0.08) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover,
      &:active {
        transform: translateY(-6rpx);
        box-shadow: 0 12rpx 28rpx rgba(25, 118, 210, 0.25);

        &::before {
          opacity: 1;
        }
      }

      .grid-icon-wrapper {
        width: 120rpx;
        height: 120rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 24rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 6rpx 16rpx rgba(25, 118, 210, 0.15);
        transition: all 0.3s ease;

        .grid-icon {
          width: 72rpx;
          height: 72rpx;
        }
      }

      .grid-text {
        color: #1565c0;
        font-size: 28rpx;
        font-weight: 500;
        text-align: center;
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .grid-container {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .physician-header-card {
    padding: 30rpx 20rpx;

    .physician-info {
      gap: 20rpx;

      .avatar-box {
        width: 100rpx;
        height: 100rpx;
      }

      .physician-details {
        .physician-name {
          font-size: 32rpx;
        }

        .physician-department,
        .physician-hospital {
          font-size: 24rpx;
        }
      }
    }
  }

  .no-physician-card {
    padding: 40rpx 20rpx;

    .no-physician-content {
      .logo-img {
        width: 120rpx;
        height: 120rpx;
      }

      .bind-tip {
        font-size: 28rpx;
      }

      .bind-button {
        padding: 16rpx 32rpx;
        font-size: 28rpx;
      }
    }
  }

  .grid-section {
    padding: 0 20rpx;

    .section-title {
      font-size: 32rpx;
    }

    .grid-container {
      gap: 16rpx;

      .grid-item {
        padding: 24rpx 16rpx;

        .grid-icon-wrapper {
          width: 90rpx;
          height: 90rpx;
          margin-bottom: 16rpx;

          .grid-icon {
            width: 54rpx;
            height: 54rpx;
          }
        }

        .grid-text {
          font-size: 26rpx;
        }
      }
    }
  }
}
</style>