<template>
	<view class="personalData-v">
		<PatienData ref="PatienData" v-if="baseInfo.roleId === '患者'" :personalData="patientInfo">
		</PatienData>
		<PhysicianData ref="PhysicianData" v-if="baseInfo.roleId === '医师'" :personalData="physicanInfo">
		</PhysicianData>
	</view>
</template>
<script>
import accountData from './components/accountInformation.vue';
import PatienData from './components/PatienData.vue';
import PhysicianData from './components/PhysicianData.vue';
import { getCurrentUserPatient } from '@/api/flow-up/patient';
import { getCurrentUserPhysician } from '@/api/flow-up/physician';

export default {
	components: {
		accountData,
		PatienData,
		PhysicianData
	},
	data() {
		return {
			baseInfo: {},
			patientInfo: {},
			physicanInfo: {}
		};
	},
	onLoad(e) {
		this.baseInfo = JSON.parse(decodeURIComponent(e.baseInfo));
	},
	onShow() {
		this.patientInfo = {};
		this.physicanInfo = {};
		if (this.baseInfo.roleId === '患者') {
			this.getPatientInfo();
		}
		if (this.baseInfo.roleId === '医师') {
			this.getPyhsicianInfo();
		}
	},
	methods: {
		getPatientInfo() {
			getCurrentUserPatient().then(res => {
				this.patientInfo = res.data;
			})
		},
		getPyhsicianInfo() {
			getCurrentUserPhysician().then(res => {
				this.physicanInfo = res.data;
			})

		}
	}
}
</script>

<style lang="scss">
page {
	background-color: #f0f2f6;
	height: 100%;
}

.notice-warp {
	height: 100rpx;

	.search-box {
		padding: 20rpx;
	}
}

.content {
	margin-top: 120rpx;
}

.personalData-v {
	display: flex;
	flex-direction: column;
	padding-bottom: 100rpx;

	::v-deep .buttom-btn {
		width: 100% !important;
	}
}
</style>