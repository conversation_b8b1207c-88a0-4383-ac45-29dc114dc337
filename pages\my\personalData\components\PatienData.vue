<template>
  <view class="patient-form-container">
    <!-- 顶部患者信息卡片 -->
    <view class="patient-info-card">
      <view class="patient-header">
        <view class="patient-avatar">
          <text class="avatar-text">{{ getPatientAvatarText() }}</text>
        </view>
        <view class="patient-details">
          <view class="patient-name">{{ dataForm.name || "未知患者" }}</view>
          <view class="patient-meta">
            <text class="meta-label">身份证号:</text>
            <text class="meta-value">{{ dataForm.idCard || "暂无" }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 表单区域 -->
    <view class="form-sections">
      <!-- 患者基本信息 -->
      <view class="form-section animate__animated animate__fadeInUp" :style="{ animationDelay: '0.1s' }">
        <view class="section-header" @click="toggleSection('basic')">
          <view class="section-icon">👨‍⚕️</view>
          <text class="section-title">基本信息</text>
          <view class="section-toggle">
            <text class="toggle-icon">{{ openSections.basic ? '▲' : '▼' }}</text>
          </view>
        </view>
        <view class="section-content" v-show="openSections.basic">
          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.2s' }">
            <text class="form-label required">姓名</text>
            <view class="form-field">
              <u-input v-model="dataForm.name" placeholder="请输入姓名" class="form-input" clearable border="surround" />
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.3s' }">
            <text class="form-label required">性别</text>
            <view class="form-field">
              <XundaSelect v-model="dataForm.sex" placeholder="请选择性别" :options="sexOptions" :props="props"
                class="form-select" />
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.4s' }">
            <text class="form-label required">出生日期</text>
            <view class="form-field date-field">
              <picker class="date-picker" mode="date" fields="date" :value="dataForm.birthday"
                @change="onBirthdayChange">
                <view class="date-picker-inner">
                  <text class="date-text">{{ dataForm.birthday ? formatDisplayDate(dataForm.birthday) : '请选择日期'
                    }}</text>
                  <text class="arrow-icon">▼</text>
                </view>
              </picker>
              <view v-if="dataForm.birthday" class="clear-date-btn" @click="clearBirthday">
                <text class="clear-icon">✕</text>
              </view>
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.5s' }">
            <text class="form-label">年龄</text>
            <view class="form-field">
              <u-input v-model="dataForm.age" placeholder="请输入年龄" type="number" class="form-input" clearable
                border="surround" />
            </view>
          </view>
        </view>
      </view>

      <!-- 联系信息 -->
      <view class="form-section animate__animated animate__fadeInUp" :style="{ animationDelay: '0.2s' }">
        <view class="section-header" @click="toggleSection('contact')">
          <view class="section-icon">📞</view>
          <text class="section-title">联系信息</text>
          <view class="section-toggle">
            <text class="toggle-icon">{{ openSections.contact ? '▲' : '▼' }}</text>
          </view>
        </view>
        <view class="section-content" v-show="openSections.contact">
          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.3s' }">
            <text class="form-label">联系电话</text>
            <view class="form-field">
              <u-input v-model="dataForm.mobilePhone" placeholder="请输入联系电话" class="form-input" clearable
                border="surround" />
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.4s' }">
            <text class="form-label">紧急联系人</text>
            <view class="form-field">
              <u-input v-model="dataForm.urgentContacts" placeholder="请输入紧急联系人" class="form-input" clearable
                border="surround" />
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.5s' }">
            <text class="form-label">紧急联系电话</text>
            <view class="form-field">
              <u-input v-model="dataForm.urgentTelePhone" placeholder="请输入紧急联系电话" class="form-input" clearable
                border="surround" />
            </view>
          </view>
        </view>
      </view>

      <!-- 地址信息 -->
      <view class="form-section animate__animated animate__fadeInUp" :style="{ animationDelay: '0.3s' }">
        <view class="section-header" @click="toggleSection('address')">
          <view class="section-icon">📍</view>
          <text class="section-title">地址信息</text>
          <view class="section-toggle">
            <text class="toggle-icon">{{ openSections.address ? '▲' : '▼' }}</text>
          </view>
        </view>
        <view class="section-content" v-show="openSections.address">
          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.4s' }">
            <text class="form-label">详细地址</text>
            <view class="form-field">
              <u-input v-model="dataForm.addressDetail" placeholder="请输入详细地址" class="form-input" clearable
                border="surround" />
            </view>
          </view>
        </view>
      </view>

      <!-- 住院信息 -->
      <view class="form-section animate__animated animate__fadeInUp" :style="{ animationDelay: '0.4s' }">
        <view class="section-header" @click="toggleSection('hospitalization')">
          <view class="section-icon">🏥</view>
          <text class="section-title">住院信息</text>
          <view class="section-toggle">
            <text class="toggle-icon">{{ openSections.hospitalization ? '▲' : '▼' }}</text>
          </view>
        </view>
        <view class="section-content" v-show="openSections.hospitalization">
          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.5s' }">
            <text class="form-label">住院号</text>
            <view class="form-field">
              <u-input v-model="dataForm.admissionNo" placeholder="请输入住院号" class="form-input" clearable
                border="surround" />
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.6s' }">
            <text class="form-label">入院日期</text>
            <view class="form-field date-field">
              <picker class="date-picker" mode="date" fields="date" :value="dataForm.admissionDate"
                @change="onAdmissionDateChange">
                <view class="date-picker-inner">
                  <text class="date-text">{{ dataForm.admissionDate ? formatDisplayDate(dataForm.admissionDate) :
                    '请选择日期' }}</text>
                  <text class="arrow-icon">▼</text>
                </view>
              </picker>
              <view v-if="dataForm.admissionDate" class="clear-date-btn" @click="clearAdmissionDate">
                <text class="clear-icon">✕</text>
              </view>
            </view>
          </view>

          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.7s' }">
            <text class="form-label">出院日期</text>
            <view class="form-field date-field">
              <picker class="date-picker" mode="date" fields="date" :value="dataForm.dischargeDate"
                @change="onDischargeDateChange">
                <view class="date-picker-inner">
                  <text class="date-text">{{ dataForm.dischargeDate ? formatDisplayDate(dataForm.dischargeDate) :
                    '请选择日期' }}</text>
                  <text class="arrow-icon">▼</text>
                </view>
              </picker>
              <view v-if="dataForm.dischargeDate" class="clear-date-btn" @click="clearDischargeDate">
                <text class="clear-icon">✕</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 病史信息 -->
      <view class="form-section animate__animated animate__fadeInUp" :style="{ animationDelay: '0.5s' }">
        <view class="section-header" @click="toggleSection('medical')">
          <view class="section-icon">📋</view>
          <text class="section-title">病史信息</text>
          <view class="section-toggle">
            <text class="toggle-icon">{{ openSections.medical ? '▲' : '▼' }}</text>
          </view>
        </view>
        <view class="section-content" v-show="openSections.medical">
          <view class="form-item animate__animated animate__fadeIn" :style="{ animationDelay: '0.6s' }">
            <text class="form-label">过敏史</text>
            <view class="form-field">
              <u-input v-model="dataForm.admittingDiagnosis" placeholder="请输入过敏史" type="textarea" class="form-textarea"
                clearable border="surround" auto-height />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="action-buttons">
      <view class="action-row">
        <view class="action-btn secondary-btn" @click="resetForm" hover-class="button-hover">
          <view class="btn-icon">↩️</view>
          <text class="btn-text">取消</text>
        </view>
        <view class="action-btn primary-btn" @click="submit" hover-class="button-hover">
          <view class="btn-icon">✅</view>
          <text class="btn-text">保存</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { UpdateUser } from "@/api/common";
import { useBaseStore } from "@/store/modules/base";
import xunda from "../../../../utils/xunda";
import { getInfo, create, update as updatePatient } from "@/api/flow-up/patient";

const baseStore = useBaseStore();
export default {
  props: {
    personalData: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    const data = {
      show: false,
      props: {
        label: "fullName",
        value: "enCode",
      },
      // 添加用于控制各部分展开/收起的状态
      openSections: {
        basic: true,
        contact: true,
        address: true,
        hospitalization: true,
        medical: true
      },
      dataForm: {
        id: "",
        name: "",
        birthday: "",
        age: null,
        mobilePhone: "",
        urgentContacts: "",
        urgentTelePhone: "",
        postalAddress: "",
        addressDetail: "",
        admissionNo: "",
        admissionDate: "",
        dischargeDate: "",
        admittingDiagnosis: "",
        pipeBedPhysician: "",
        allergyHistory: "",
        medicalHistory: "",
        familyMedicalHistory: "",
        creatorTime: null,
        sex: "",
        idCard: "",
        address: null,
        latitude: null,
        longitude: null,
        physicianName: null,
        rate: null,
        type: null,
        userId: "",
        fcount: null,
        tnumber: null,
        dnumber: null
      },
      sexOptions: [],
      rules: {
        realName: [
          {
            required: true,
            message: "请输入姓名",
            trigger: ["change", "blur"],
          },
        ],
      },
    };
    return data;
  },
  computed: {
    baseURL() {
      return this.define.baseURL;
    },
  },
  watch: {
    personalData: {
      handler(val) {
        this.init();
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
  },
  methods: {
    // 添加日期格式化方法（用于显示）
    formatDisplayDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${year}年${month}月${day}日`;
    },

    // 获取患者头像文字
    getPatientAvatarText() {
      const name = this.dataForm.name;
      if (!name) return "患";
      return name.length > 1 ? name.slice(-2) : name;
    },

    init() {
      let initData = JSON.parse(JSON.stringify(this.personalData));
      for (let key in initData) {
        for (let k in this.dataForm) {
          if (key === k) {
            this.dataForm[key] = initData[key];
          }
        }
      }
      this.getOptions();
    },

    getOptions() {
      const baseStore = useBaseStore();
      baseStore.getDictionaryData({ sort: "sex" }).then((res) => {
        this.sexOptions = JSON.parse(JSON.stringify(res));
      });
      this.show = true;
    },

    // 清除输入框内容
    clearInput(field) {
      this.dataForm[field] = '';
    },

    // 处理出生日期变更
    onBirthdayChange(e) {
      this.dataForm.birthday = e.detail.value;
    },

    // 处理入院日期变更
    onAdmissionDateChange(e) {
      this.dataForm.admissionDate = e.detail.value;
    },

    // 处理出院日期变更
    onDischargeDateChange(e) {
      this.dataForm.dischargeDate = e.detail.value;
    },

    // 清除出生日期
    clearBirthday() {
      this.dataForm.birthday = '';
    },

    // 清除入院日期
    clearAdmissionDate() {
      this.dataForm.admissionDate = '';
    },

    // 清除出院日期
    clearDischargeDate() {
      this.dataForm.dischargeDate = '';
    },

    // 切换表单部分的展开/收起状态
    toggleSection(sectionName) {
      this.openSections[sectionName] = !this.openSections[sectionName];
    },

    submit() {
      // 注意：原代码中使用了 this.$refs.dataForm.validate，但模板中没有 ref="dataForm"
      // 这里保持原有逻辑，但实际应该添加表单验证
      updatePatient(this.dataForm).then((res) => {
        uni.showToast({
          title: "保存成功",
          duration: 800,
          icon: "none",
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1000);
      });
    },

    // 重置表单
    resetForm() {
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  min-height: 100vh;
}

.patient-form-container {
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 200rpx;
}

// 顶部患者信息卡片
.patient-info-card {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 12rpx 40rpx rgba(25, 118, 210, 0.3);
  color: #fff;

  .patient-header {
    display: flex;
    align-items: center;
    gap: 24rpx;

    .patient-avatar {
      width: 80rpx;
      height: 80rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 3rpx solid rgba(255, 255, 255, 0.3);

      .avatar-text {
        color: #fff;
        font-size: 28rpx;
        font-weight: 700;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
      }
    }

    .patient-details {
      flex: 1;
      min-width: 0;

      .patient-name {
        font-size: 36rpx;
        font-weight: 700;
        color: #fff;
        margin-bottom: 12rpx;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
      }

      .patient-meta {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .meta-label {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
        }

        .meta-value {
          font-size: 26rpx;
          color: #fff;
          font-weight: 600;
        }
      }
    }
  }
}

// 表单区域
.form-sections {
  .form-section {
    background: #fff;
    border-radius: 20rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(25, 118, 210, 0.08);
    border: 1rpx solid rgba(25, 118, 210, 0.1);
    transition: all 0.3s ease;

    .section-header {
      background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 100%);
      padding: 24rpx 28rpx;
      display: flex;
      align-items: center;
      gap: 16rpx;
      border-bottom: 1rpx solid rgba(25, 118, 210, 0.1);
      cursor: pointer;
      user-select: none;

      .section-icon {
        font-size: 32rpx;
      }

      .section-title {
        font-size: 32rpx;
        font-weight: 700;
        color: #1565c0;
        flex: 1;
      }

      .section-toggle {
        .toggle-icon {
          font-size: 28rpx;
          color: #1565c0;
          transition: transform 0.3s ease;
        }
      }
    }

    .section-content {
      padding: 28rpx;
    }

    .form-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 24rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .form-label {
        width: 180rpx;
        font-size: 28rpx;
        color: #1565c0;
        font-weight: 600;
        margin-right: 20rpx;
        flex-shrink: 0;
        padding-top: 10rpx;

        &.required::after {
          content: "*";
          color: #f44336;
          margin-left: 4rpx;
        }
      }

      .form-field {
        flex: 1;
        min-width: 0;

        &.date-field {
          display: flex;
          align-items: center;
          gap: 16rpx;
        }
      }

      .form-input,
      .form-select,
      .form-datepicker,
      .form-textarea {

        ::v-deep .u-input,
        ::v-deep .xunda-select,
        ::v-deep .xunda-date-picker,
        ::v-deep .u-textarea {
          width: 100%;
          min-height: 60rpx;
          padding: 20rpx;
          border: 2rpx solid #e9ecef;
          border-radius: 16rpx;
          font-size: 28rpx;
          color: #333;
          background: #fff;
          transition: all 0.3s ease;
          box-sizing: border-box;

          &:focus {
            border-color: #1976d2;
            box-shadow: 0 0 0 6rpx rgba(25, 118, 210, 0.1);
            outline: none;
          }

          &::placeholder {
            color: #adb5bd;
            font-size: 26rpx;
          }
        }

        ::v-deep .u-textarea {
          min-height: 120rpx;
          resize: none;
        }
      }

      .date-picker {
        flex: 1;

        .date-picker-inner {
          width: 100%;
          min-height: 60rpx;
          padding: 20rpx;
          border: 2rpx solid #e9ecef;
          border-radius: 16rpx;
          font-size: 28rpx;
          color: #333;
          background: #fff;
          transition: all 0.3s ease;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .date-text {
            flex: 1;
          }

          .arrow-icon {
            color: #999;
            font-size: 24rpx;
          }
        }
      }

      .clear-input-btn,
      .clear-date-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border-radius: 16rpx;
        border: 2rpx solid #e9ecef;

        .clear-icon {
          color: #999;
          font-size: 28rpx;
        }
      }
    }
  }
}

// 底部操作按钮
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 20rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -8rpx 32rpx rgba(25, 118, 210, 0.15);
  border-top: 1rpx solid rgba(25, 118, 210, 0.1);

  .action-row {
    display: flex;
    gap: 16rpx;

    .action-btn {
      flex: 1;
      height: 88rpx;
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12rpx;
      font-weight: 600;
      transition: all 0.3s ease;
      border: 2rpx solid transparent;

      .btn-icon {
        font-size: 28rpx;
      }

      .btn-text {
        font-size: 28rpx;
      }

      &.primary-btn {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        color: #fff;
        box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.3);

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.3);
        }
      }

      &.secondary-btn {
        background: #f8f9fa;
        color: #6c757d;
        border-color: #dee2e6;

        &:active {
          background: #e9ecef;
        }
      }

      &.button-hover {
        transform: scale(0.98);
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .patient-form-container {
    padding: 16rpx;
    padding-bottom: 180rpx;
  }

  .patient-info-card {
    padding: 24rpx;

    .patient-header {
      gap: 16rpx;

      .patient-avatar {
        width: 64rpx;
        height: 64rpx;

        .avatar-text {
          font-size: 24rpx;
        }
      }

      .patient-details {
        .patient-name {
          font-size: 32rpx;
        }

        .patient-meta {
          .meta-label {
            font-size: 22rpx;
          }

          .meta-value {
            font-size: 24rpx;
          }
        }
      }
    }
  }

  .form-sections {
    .form-section {
      .section-header {
        padding: 20rpx 24rpx;

        .section-icon {
          font-size: 28rpx;
        }

        .section-title {
          font-size: 28rpx;
        }
      }

      .section-content {
        padding: 20rpx;

        .form-item {
          margin-bottom: 16rpx;

          .form-label {
            font-size: 26rpx;
            width: 160rpx;
          }

          ::v-deep .u-input,
          ::v-deep .xunda-select,
          ::v-deep .xunda-date-picker,
          ::v-deep .u-textarea {
            padding: 16rpx;
            font-size: 26rpx;

            &::placeholder {
              font-size: 24rpx;
            }
          }

          .date-picker {
            .date-picker-inner {
              padding: 16rpx;
              font-size: 26rpx;
            }
          }

          .clear-input-btn,
          .clear-date-btn {
            width: 56rpx;
            height: 56rpx;
          }
        }
      }
    }
  }

  .action-buttons {
    padding: 20rpx 16rpx;

    .action-row {
      gap: 12rpx;

      .action-btn {
        height: 80rpx;

        .btn-icon {
          font-size: 24rpx;
        }

        .btn-text {
          font-size: 26rpx;
        }
      }
    }
  }
}
</style>