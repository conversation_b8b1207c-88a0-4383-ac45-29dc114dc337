<template>
  <view class="video-container">
    <view class="video-player">
      <view class="video_wrap">

        <m-video id="myVideo" :src="videoUrl" controls autoplay token="JdFC0p7JI8QYU53heQgvuGb+nbUPhV8EXjT/sB4Wzec=">
        </m-video>
      </view>
      <view class="video-info">
        <view class="video-title">{{ videoData.title }}</view>
        <view class="video-tags" v-if="videoData.tags && Array.isArray(videoData.tags) && videoData.tags.length > 0">
          <view class="tag" v-for="(tag, index) in videoData.tags" :key="index">{{ tag }}</view>
        </view>
        <view class="video-meta">
          <text class="video-time">{{ xunda.toDate(videoData.publishTime) }}</text>
          <text class="video-author">作者：{{ videoData.author }}</text>
          <text class="video-views">{{ videoData.viewCount }}次播放</text>
        </view>
        <view class="video-desc">{{ videoData.summary }}</view>
      </view>
    </view>
    <view class="related-videos" v-if='false'>
      <view class="section-title">相关视频</view>
      <view class="video-item" v-for="(item, index) in relatedVideos" :key="index" @click="playVideo(item)">
        <image class="video-thumb" :src="item.thumb"></image>
        <view class="video-info">
          <view class="video-title">{{ item.title }}</view>
          <view class="video-duration">{{ item.duration }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getNewsDetail, getRelatedNews } from '@/api/flow-up/news';
import xunda from '@/utils/xunda';
export default {
  data() {
    return {
      videoData: {
      },
      relatedVideos: [
        {
          url: "https://example.com/video2.mp4",
          thumb: "https://picsum.photos/300/200?random=3",
          title: "海姆立克急救法",
          duration: "03:45",
        },
        {
          url: "https://example.com/video3.mp4",
          thumb: "https://picsum.photos/300/200?random=4",
          title: "创伤止血包扎",
          duration: "04:20",
        },
      ],
      videoContext: null,
    };
  },
  async onLoad(options) {
    if (!options.id) {
      uni.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
      return;
    }

    uni.showLoading({
      title: '加载中...',
      mask: true
    });

    try {
      // 并行请求新闻详情和相关新闻
      const [detailRes, relatedRes] = await Promise.all([
        getNewsDetail(options.id),
        // getRelatedNews(options.id)
      ]);

      if (detailRes.code === 200 && detailRes.data) {
        this.videoData = detailRes.data
      } else {
        throw new Error('获取新闻详情失败');
      }

      // if (relatedRes.code === 200) {
      //   this.relatedNews = relatedRes.data || [];
      // }
    } catch (error) {
      console.error('加载新闻详情失败:', error);
      uni.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      uni.hideLoading();
      this.isLoading = false;
    }
  },
  onReady() {
    this.videoContext = uni.createVideoContext("myVideo", this);
  },
  methods: {
    playVideo(item) {
      this.videoData = {
        ...item,
        time: "2023-06-08",
        views: 980,
        desc: "专业医师演示" + item.title + "的标准操作流程...",
      };
      this.$nextTick(() => {
        this.videoContext.play();
      });
    },
    goBack() {
      uni.navigateBack();
    },
  },
  computed: {
    videoUrl() {
      if (this.videoData.videoContent && this.videoData.videoContent.length > 0)
        return this.define.baseURL + this.videoData.videoContent[0].url; else return '';
    },
  },

};
</script>

<style lang="scss">
.video-container {
  background-color: #f8faff;
  min-height: 100vh;

  .video-player {
    background: linear-gradient(to bottom, #000000, #1a1a1a);
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
    position: relative;

    .video_wrap {
      width: 100%;
      height: 420rpx;
    }

    .video-info {
      padding: 30rpx;
      background: #ffffff;
      border-radius: 30rpx 30rpx 0 0;
      margin-top: -10rpx;
      position: relative;
      box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);

      .video-title {
        font-size: 36rpx;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 20rpx;
        line-height: 1.4;
      }

      .video-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;
        margin-bottom: 20rpx;
        
        .tag {
          padding: 6rpx 20rpx;
          background: #ecf5ff;
          color: #409eff;
          border-radius: 20rpx;
          font-size: 24rpx;
        }
      }

      .video-meta {
        display: flex;
        align-items: center;
        gap: 30rpx;
        font-size: 26rpx;
        color: #94a3b8;
        margin-bottom: 30rpx;

        text {
          position: relative;

          &:not(:last-child):after {
            content: "";
            position: absolute;
            right: -15rpx;
            top: 50%;
            width: 4rpx;
            height: 4rpx;
            background: #94a3b8;
            border-radius: 50%;
            transform: translateY(-50%);
          }
        }

        .video-views {
          color: #10b981;
          font-weight: 500;
        }
      }

      .video-desc {
        font-size: 28rpx;
        color: #64748b;
        line-height: 1.6;
        background: #f8fafc;
        padding: 20rpx;
        border-radius: 16rpx;
        position: relative;

        &::before {
          content: "";
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4rpx;
          background: #e2e8f0;
          border-radius: 2rpx;
        }
      }
    }
  }

  .related-videos {
    margin: 20rpx;
    background: #ffffff;
    padding: 30rpx;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 30rpx;
      padding-left: 20rpx;
      border-left: 8rpx solid #409eff;
      display: flex;
      align-items: center;
    }

    .video-item {
      display: flex;
      margin-bottom: 30rpx;
      padding-bottom: 30rpx;
      border-bottom: 2rpx solid #f1f5f9;
      transition: transform 0.3s ease;

      &:active {
        transform: scale(0.98);
      }

      &:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
      }

      .video-thumb {
        width: 240rpx;
        height: 160rpx;
        border-radius: 12rpx;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
        object-fit: cover;
      }

      .video-info {
        flex: 1;
        padding-left: 24rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .video-title {
          font-size: 30rpx;
          color: #2c3e50;
          margin-bottom: 16rpx;
          line-height: 1.4;
          font-weight: 500;

          // 文本超出两行显示省略号
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }

        .video-duration {
          font-size: 24rpx;
          color: #94a3b8;
          display: flex;
          align-items: center;
          gap: 8rpx;

          &::before {
            content: "";
            display: inline-block;
            width: 6rpx;
            height: 6rpx;
            background: #94a3b8;
            border-radius: 50%;
          }
        }
      }
    }
  }
}
</style>
