<template>
  <view class="news-detail-container">
    <view class="news-header">
      <view class="news-title">{{ newsData.title }}</view>
      <!-- 添加标签显示 -->
      <view class="news-tags" v-if="newsData.tags && Array.isArray(newsData.tags) && newsData.tags.length > 0">
        <view class="tag" v-for="(tag, index) in newsData.tags" :key="index">{{ tag }}</view>
      </view>
      <view class="news-meta">
        <text class="news-time">{{ xunda.toDate(newsData.publishTime) }}</text>
        <text class="news-author">作者：{{ newsData.author }}</text>
        <text class="news-views">{{ newsData.viewCount }}次阅读</text>
      </view>
    </view>

    <image class="news-image" :src="newsData.coverImage" mode="widthFix" :lazy-loaded="imageLoaded"
      @load="imageLoaded = true"></image>

    <view class="news-content">
      <!-- 添加内容区域标题 -->
      <view class="content-title">正文</view>
      <rich-text :nodes="newsData.content"></rich-text>
    </view>

    <view class="related-news" v-if="relatedNews.length > 0">
      <view class="section-title">相关推荐</view>
      <view class="news-item" v-for="(item, index) in relatedNews" :key="index" @click="viewDetail(item)">
        <view class="news-title">{{ item.title }}</view>
        <view class="news-time">{{ xunda.toDate(item.publishTime) }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import { getNewsDetail, getRelatedNews } from "@/api/flow-up/news";
import xunda from "@/utils/xunda.js";

export default {
  data() {
    return {
      newsData: {
        id: "",
        title: "",
        publishTime: "",
        author: "",
        coverImage: "",
        content: "",
        tags: [],
        viewCount: 0,
      },
      relatedNews: [],
      isLoading: true,
      imageLoaded: false,
    };
  },
  async onLoad(options) {
    if (!options.id) {
      uni.showToast({
        title: "参数错误",
        icon: "none",
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
      return;
    }

    uni.showLoading({
      title: "加载中...",
      mask: true,
    });

    try {
      // 并行请求新闻详情和相关新闻
      const [detailRes, relatedRes] = await Promise.all([
        getNewsDetail(options.id),
        // getRelatedNews(options.id)
      ]);

      if (detailRes.code === 200 && detailRes.data) {
        this.newsData = {
          id: detailRes.data.id,
          title: detailRes.data.title,
          publishTime: detailRes.data.publishTime,
          author: detailRes.data.author,
          coverImage: this.define.baseURL + detailRes.data.coverImage,
          content: this.formatContent(detailRes.data.content),
          tags: detailRes.data.tags || [],
          viewCount: detailRes.data.viewCount || 0,
        };
      } else {
        throw new Error("获取新闻详情失败");
      }

      // if (relatedRes.code === 200) {
      //   this.relatedNews = relatedRes.data || [];
      // }
    } catch (error) {
      console.error("加载新闻详情失败:", error);
      uni.showToast({
        title: "加载失败，请重试",
        icon: "none",
      });
    } finally {
      uni.hideLoading();
      this.isLoading = false;
    }
  },
  methods: {
    viewDetail(item) {
      uni.redirectTo({
        url: `/pages/news/detail?id=${item.id}`,
      });
    },

    goBack() {
      uni.navigateBack();
    },

    formatContent(content) {
      if (!content) return "";

      // 如果content是HTML字符串，直接返回
      if (typeof content === "string" && content.includes("<")) {
        return content;
      }

      // 如果是普通文本，转换为HTML格式
      return content
        .split("\n")
        .map((text) => `<text>${text}</text>`)
        .join("");
    },
  },
};
</script>

<style lang="scss">
.news-detail-container {
  background-color: #f8faff;
  min-height: 100vh;

  .news-header {
    background: linear-gradient(to bottom, #ffffff, #f8faff);
    padding: 30rpx;
    border-radius: 0 0 30rpx 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
    position: relative;

    .news-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 20rpx;
      line-height: 1.4;
    }

    // 添加标签样式
    .news-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
      margin-bottom: 20rpx;

      .tag {
        padding: 6rpx 20rpx;
        background: #ecf5ff;
        color: #409eff;
        border-radius: 20rpx;
        font-size: 24rpx;
      }
    }

    .news-meta {
      display: flex;
      align-items: center;
      gap: 30rpx;
      font-size: 26rpx;
      color: #94a3b8;
      margin-bottom: 10rpx;

      text {
        position: relative;

        &:not(:last-child):after {
          content: "";
          position: absolute;
          right: -15rpx;
          top: 50%;
          width: 4rpx;
          height: 4rpx;
          background: #94a3b8;
          border-radius: 50%;
          transform: translateY(-50%);
        }
      }

      .news-views {
        color: #10b981;
        font-weight: 500;
      }
    }
  }

  .news-image {
    width: 100%;
    display: block;
    margin: 20rpx 0;
    border-radius: 16rpx;
    box-shadow: 0 10rpx 30rpx rgba(74, 134, 232, 0.15);
  }

  .news-content {
    background: #fff;
    margin: 20rpx;
    padding: 40rpx 30rpx;
    font-size: 32rpx;
    line-height: 1.8;
    color: #374151;
    border-radius: 20rpx;
    box-shadow: 0 8rpx 30rpx rgba(74, 134, 232, 0.08);

    // 添加内容区域标题样式
    .content-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 30rpx;
      padding-left: 20rpx;
      border-left: 8rpx solid #409eff;
      display: flex;
      align-items: center;
    }

    p {
      margin-bottom: 30rpx;
      text-align: justify;

      &:last-child {
        margin-bottom: 0;
      }
    }

    img {
      max-width: 100%;
      border-radius: 12rpx;
      margin: 20rpx 0;
    }

    a {
      color: #4a86e8;
      text-decoration: none;
      border-bottom: 2rpx solid #4a86e8;
      padding-bottom: 2rpx;
    }
  }

  .related-news {
    margin: 20rpx;
    background: #ffffff;
    padding: 30rpx;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 30rpx;
      padding-left: 20rpx;
      border-left: 8rpx solid #409eff;
      display: flex;
      align-items: center;
    }

    .news-item {
      display: flex;
      flex-direction: column;
      padding: 24rpx;
      margin-bottom: 30rpx;
      border-radius: 12rpx;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
      background: linear-gradient(to right, #ffffff, #f8faff);

      &:active {
        transform: scale(0.98);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .news-title {
        font-size: 30rpx;
        color: #2c3e50;
        margin-bottom: 16rpx;
        line-height: 1.4;
        font-weight: 500;

        // 文本超出两行显示省略号
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }

      .news-time {
        font-size: 24rpx;
        color: #94a3b8;
        display: flex;
        align-items: center;
        gap: 8rpx;

        &::before {
          content: "";
          display: inline-block;
          width: 6rpx;
          height: 6rpx;
          background: #94a3b8;
          border-radius: 50%;
        }
      }
    }
  }
}
</style>