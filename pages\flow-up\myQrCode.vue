<template>
  <view class="qrcode-page">
    <!-- 头部导航 -->
    <u-navbar title="我的二维码名片" :border="false" :autoBack="true" bgColor="#ffffff"></u-navbar>

    <!-- 卡片内容 -->
    <view class="qrcode-card">
      <view class="card-content">
        <view class="qrcode-container">
          <image class="qrcode" :src="qrCodeUrl" mode="aspectFit"></image>
          <view class="scan-tip">扫一扫，绑定随访医师</view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-item" @click="saveToAlbum">
        <view class="action-icon">
          <u-icon name="photo" size="44" color="#1976d2"></u-icon>
        </view>
        <text class="action-text">保存到相册</text>
      </view>
      <view class="action-item" @click="shareQRCode">
        <view class="action-icon">
          <u-icon name="share" size="44" color="#1976d2"></u-icon>
        </view>
        <text class="action-text">分享二维码</text>
      </view>
    </view>
  </view>
</template>

<script>
import { generateQRCodeAsync } from "@/api/flow-up/physician";
import { UserSettingInfo } from "@/api/common";

export default {
  data() {
    return {
      defaultAvatar: "/static/image/default-avatar.png",
      userInfo: {},
      qrCodeUrl: "", // 二维码图片URL
    };
  },
  onLoad() {
    this.getUserInfo();
    this.generateQRCode();
  },
  methods: {
    // 获取用户信息
    async getUserInfo() {
      try {
        const res = await UserSettingInfo();
        this.userInfo = res.data || {};
      } catch (error) {
        console.error("获取用户信息失败:", error);
        uni.showToast({
          title: "获取用户信息失败",
          icon: "none"
        });
      }
    },

    // 生成二维码
    async generateQRCode() {
      try {
        const res = await generateQRCodeAsync({ id: "" });
        this.qrCodeUrl = this.define.baseURL + res.data;
      } catch (error) {
        console.error("生成二维码失败:", error);
        uni.showToast({
          title: "生成二维码失败",
          icon: "none"
        });
      }
    },

    // 保存到相册
    async saveToAlbum() {
      if (!this.qrCodeUrl) {
        uni.showToast({
          title: "二维码尚未生成",
          icon: "none"
        });
        return;
      }

      try {
        // #ifdef H5
        uni.showToast({
          title: "请长按二维码保存",
          icon: "none"
        });
        // #endif

        // #ifndef H5
        const res = await uni.downloadFile({
          url: this.qrCodeUrl
        });

        if (res.statusCode === 200) {
          await uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath
          });

          uni.showToast({
            title: "保存成功",
            icon: "success"
          });
        } else {
          throw new Error("下载失败");
        }
        // #endif
      } catch (error) {
        console.error("保存失败:", error);
        uni.showToast({
          title: "保存失败，请重试",
          icon: "none"
        });
      }
    },

    // 分享二维码
    shareQRCode() {
      if (!this.qrCodeUrl) {
        uni.showToast({
          title: "二维码尚未生成",
          icon: "none"
        });
        return;
      }

      // #ifdef MP-WEIXIN
      uni.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 2,
        imageUrl: this.qrCodeUrl,
        success: () => {
          uni.showToast({
            title: "分享成功",
            icon: "success"
          });
        },
        fail: () => {
          uni.showToast({
            title: "分享失败",
            icon: "none"
          });
        }
      });
      // #endif

      // #ifdef H5
      uni.showToast({
        title: "请长按二维码分享",
        icon: "none"
      });
      // #endif
    },
  }
};
</script>

<style lang="scss">
.qrcode-page {
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 180rpx;
}

.qrcode-card {
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 32rpx rgba(25, 118, 210, 0.15);
  margin: 20rpx;

  .card-header {
    padding: 40rpx 32rpx;

    .user-info {
      display: flex;
      align-items: center;

      .avatar {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
        border: 4rpx solid rgba(255, 255, 255, 0.3);
        margin-right: 24rpx;
      }

      .info-text {
        flex: 1;

        .nickname {
          font-size: 36rpx;
          font-weight: 600;
          color: #ffffff;
          margin-bottom: 12rpx;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
        }

        .user-meta {
          display: flex;
          gap: 20rpx;

          .meta-item {
            font-size: 24rpx;
            color: rgba(255, 255, 255, 0.9);
            background: rgba(255, 255, 255, 0.2);
            padding: 6rpx 16rpx;
            border-radius: 20rpx;
          }
        }
      }
    }
  }

  .card-content {
    padding: 40rpx 32rpx;

    .qrcode-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 32rpx;

      .qrcode {
        width: 400rpx;
        height: 400rpx;
        margin: 20rpx 0 32rpx;
        border: 2rpx solid #e0e0e0;
        border-radius: 16rpx;
        padding: 20rpx;
        background: #fff;
        box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.1);
      }

      .scan-tip {
        font-size: 32rpx;
        font-weight: 600;
        color: #1565c0;
        text-align: center;
      }
    }

    .card-footer {
      .tips {
        display: flex;
        align-items: flex-start;
        gap: 12rpx;
        padding: 24rpx;
        background: #f8fbff;
        border-radius: 16rpx;
        border: 1rpx solid #e3f2fd;

        .tips-text {
          flex: 1;
          font-size: 24rpx;
          color: #6c757d;
          line-height: 1.5;
        }
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 40rpx;
  left: 30rpx;
  right: 30rpx;
  display: flex;
  justify-content: space-between;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(25, 118, 210, 0.2);
  border: 1rpx solid rgba(25, 118, 210, 0.1);

  .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    padding: 16rpx 0;

    .action-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background: #f8fbff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 12rpx;
      transition: all 0.3s ease;
    }

    .action-text {
      font-size: 24rpx;
      color: #1565c0;
      font-weight: 500;
    }

    &:active {
      .action-icon {
        transform: scale(0.95);
        background: #e3f2fd;
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .qrcode-page {
    padding: 16rpx;
    padding-bottom: 160rpx;
  }

  .qrcode-card {
    margin: 16rpx;

    .card-header {
      padding: 30rpx 24rpx;

      .user-info {
        .avatar {
          width: 80rpx;
          height: 80rpx;
          margin-right: 20rpx;
        }

        .info-text {
          .nickname {
            font-size: 32rpx;
          }

          .user-meta {
            gap: 12rpx;

            .meta-item {
              font-size: 20rpx;
              padding: 4rpx 12rpx;
            }
          }
        }
      }
    }

    .card-content {
      padding: 30rpx 24rpx;

      .qrcode-container {
        .qrcode {
          width: 320rpx;
          height: 320rpx;
        }

        .scan-tip {
          font-size: 28rpx;
        }
      }

      .card-footer {
        .tips {
          padding: 16rpx;

          .tips-text {
            font-size: 22rpx;
          }
        }
      }
    }
  }

  .bottom-actions {
    bottom: 20rpx;
    left: 20rpx;
    right: 20rpx;
    padding: 16rpx;

    .action-item {
      padding: 12rpx 0;

      .action-icon {
        width: 64rpx;
        height: 64rpx;
      }

      .action-text {
        font-size: 22rpx;
      }
    }
  }
}
</style>