import request from '@/utils/request'


const physician<PERSON><PERSON> = `/api/flowUp/physician`;


export const getDetailAsync = function (id) {
    return request({
        url: physician<PERSON><PERSON> + "/detail/" + id,
        method: "get",
    })
}


// 获取应用菜单
export function getMyPhysicianAsync(data) {
    // 发起异步请求
    return request({
        url: physician<PERSON><PERSON> + '/getMyPhysician',
        method: 'get',
        data,
        options: {
            load: false
        }
    })
}

export function generateQRCodeAsync(data) {
    return request({
        url: physician<PERSON><PERSON> + '/generateQRCode',
        method: 'get',
        data: data,
    })
}

export function scanBindAsync(data) {
    return request({
        url: physician<PERSON><PERSON> + '/scanBind',
        method: 'POST',
        data,
        options: {
            load: false
        }
    })
}

export function bindPhysicianAsync(data) {
    return request({
        url: physician<PERSON><PERSON> + '/bindPhysician',
        method: 'POST',
        data,
        options: {
            load: false
        }
    })
}


export function removeBindAsync(data) {
    return request({
        url: physician<PERSON><PERSON> + '/removeBind',
        method: 'POST',
        data,
        options: {
            load: false
        }
    })
}

export function getCurrentUserPhysician() {
    return request({
        url: patient<PERSON>pi + `/getCurrentUserPhysician`,
        method: "get",
    });
}